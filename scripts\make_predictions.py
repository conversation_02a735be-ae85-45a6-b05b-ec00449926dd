"""
AI Football Predictions Script: Predicts the likelihood of over 2.5 goals in upcoming football matches.

This script loads pre-trained machine learning models and match data to predict whether upcoming football matches will end with more than 2.5 goals. The predictions are then formatted into a Telegram-ready message that can be shared directly. 

How to run:
1. Ensure the necessary data and model files are in the specified directories.
2. Run the script with the appropriate arguments to generate predictions.
-----------------------------------------------------------------------------

Example usage, it is suggested to run the script in the root directory:

    python scripts/make_predictions.py --input_leagues_models_dir models --input_data_predict_dir data/processed --final_predictions_out_file data/final_predictions.txt --next_matches data/next_matches.json

Required Libraries:
- pandas
- numpy
- pickle
- argparse
- datetime
- json
- shutil
"""

import pandas as pd
import os
import json
import pickle
import numpy as np
from datetime import datetime
import argparse
import shutil

# Define global constants
VALID_LEAGUES = [
    "E0", "E1", "E2", "E3", "EC",  # الدوريات الإنجليزية
    "SC0", "SC1", "SC2", "SC3",  # الدوريات الاسكتلندية
    "I1", "I2",  # الدوريات الإيطالية
    "D1", "D2",  # الدوريات الألمانية
    "SP1", "SP2",  # الدوريات الإسبانية
    "F1", "F2",  # الدوريات الفرنسية
    "N1",  # الدوري الهولندي
    "P1",  # الدوري البرتغالي
    "B1",  # الدوري البلجيكي
    "T1",  # الدوري التركي
    "G1",  # الدوري اليوناني
    "ARG", "BRA", "URY", "CHL", "COL", "MEX",  # الدوريات الأمريكية
    "USA", "CAN",  # دوريات أمريكا الشمالية
    "JPN", "KOR", "CHN",  # الدوريات الآسيوية
    "SAU", "QAT", "UAE",  # دوريات الخليج
    "EGY", "MAR", "TUN", "RSA",  # الدوريات الأفريقية
    "AUS", "NZL"  # دوريات أوقيانوسيا
]

# Define the features for home team, away team, and general match information
HOME_TEAM_FEATURES = [
    'HomeTeam', 'FTHG', 'HG', 'HTHG', 'HS', 'HST', 'HHW', 'HC', 'HF', 'HFKC', 'HO', 'HY', 'HR', 'HBP',
    'B365H', 'BFH', 'BSH', 'BWH', 'GBH', 'IWH', 'LBH', 'PSH', 'SOH', 'SBH', 'SJH', 'SYH', 'VCH', 'WHH',
    'BbMxH', 'BbAvH', 'MaxH', 'AvgH', 'BFEH', 'BbMxAHH', 'BbAvAHH', 'GBAHH', 'LBAHH', 'B365AHH', 'PAHH',
    'MaxAHH', 'AvgAHH', 'BbAHh', 'AHh', 'GBAH', 'LBAH', 'B365AH', 'AvgHomeGoalsScored', 'AvgHomeGoalsConceded',
    'HomeOver2.5Perc', 'AvgLast5HomeGoalsScored', 'AvgLast5HomeGoalsConceded', 'Last5HomeOver2.5Count', 'Last5HomeOver2.5Perc'
]

AWAY_TEAM_FEATURES = [
    'AwayTeam', 'FTAG', 'AG', 'HTAG', 'AS', 'AST', 'AHW', 'AC', 'AF', 'AFKC', 'AO', 'AY', 'AR', 'ABP',
    'B365A', 'BFA', 'BSA', 'BWA', 'GBA', 'IWA', 'LBA', 'PSA', 'SOA', 'SBA', 'SJA', 'SYA', 'VCA', 'WHA',
    'BbMxA', 'BbAvA', 'MaxA', 'AvgA', 'BFEA', 'BbMxAHA', 'BbAvAHA', 'GBAHA', 'LBAHA', 'B365AHA', 'PAHA',
    'MaxAHA', 'AvgAHA', 'AvgAwayGoalsScored', 'AvgAwayGoalsConceded', 'AwayOver2.5Perc', 'AvgLast5AwayGoalsScored',
    'AvgLast5AwayGoalsConceded', 'Last5AwayOver2.5Count', 'Last5AwayOver2.5Perc'
]

"""
The general features are common to both home and away teams and contain match information that is not specific to either team.
This list in no longer necessary because in case that a feature is not in the home or away team features, it will be considered as a general feature.
GENERAL_FEATURES = [
    'Div', 'Date', 'Time', 'FTR', 'Res', 'HTR', 'Attendance', 'Referee', 'Bb1X2', 'BbMxD', 'BbAvD', 'MaxD', 'AvgD',
    'B365D', 'BFD', 'BSD', 'BWD', 'GBD', 'IWD', 'LBD', 'PSD', 'SOD', 'SBD', 'SJD', 'SYD', 'VCD', 'WHD', 'BbOU',
    'BbMx>2.5', 'BbAv>2.5', 'BbMx<2.5', 'BbAv<2.5', 'GB>2.5', 'GB<2.5', 'B365>2.5', 'B365<2.5', 'P>2.5', 'P<2.5',
    'Max>2.5', 'Max<2.5', 'Avg>2.5', 'AvgC>2.5', 'Avg<2.5', 'AvgC<2.5', 'MaxCAHA', 'MaxC>2.5', 'B365C<2.5', 'MaxCA',
    'B365CAHH', 'BbAH', 'Over2.5'
]
"""

def load_model(filepath: str):
    """Loads the machine learning model from a specified pickle file.
    
    Args:
        filepath (str): Path to the pickle file containing the model.
    
    Returns:
        model: The loaded machine learning model.
    """
    try:
        with open(filepath, 'rb') as file:
            model = pickle.load(file)
        return model
    except Exception as e:
        raise Exception(f"Error loading model: {e}")


def load_league_data(filepath: str) -> pd.DataFrame:
    """Loads the league data from a CSV file using pandas.
    
    Args:
        filepath (str): Path to the CSV file containing league data.
    
    Returns:
        pd.DataFrame: The loaded league data as a DataFrame.
    """
    # check if the file exists
    if not os.path.exists(filepath):
        raise FileNotFoundError(f"File not found: {filepath}")
    else:
        print(f"Loading data from {filepath}...")
    # Load the data from the CSV file
        return pd.read_csv(filepath)


def prepare_row_to_predict(home_team_df: pd.DataFrame, away_team_df: pd.DataFrame, numeric_columns: list) -> pd.DataFrame:
    """Prepares a DataFrame row for prediction by averaging relevant team statistics.
    
    Args:
        home_team_df (pd.DataFrame): DataFrame containing the home team's data.
        away_team_df (pd.DataFrame): DataFrame containing the away team's data.
        numeric_columns (list): List of numeric columns for prediction.
    
    Returns:
        pd.DataFrame: A single row DataFrame ready for prediction.
    """
    row_to_predict = pd.DataFrame(columns=numeric_columns)
    row_to_predict.loc[len(row_to_predict)] = [None] * len(row_to_predict.columns)

    home_team_final_df = home_team_df.head(5)[numeric_columns]
    away_team_final_df = away_team_df.head(5)[numeric_columns]

    for column in row_to_predict.columns:
        if column in HOME_TEAM_FEATURES:
            row_to_predict.loc[len(row_to_predict)-1, column] = home_team_final_df[column].mean()
        elif column in AWAY_TEAM_FEATURES:
            row_to_predict.loc[len(row_to_predict)-1, column] = away_team_final_df[column].mean()
        # If the column is not in the home or away team features, we take the average of both teams
        else:
            row_to_predict.loc[len(row_to_predict)-1, column] = (away_team_final_df[column].mean() + home_team_final_df[column].mean()) / 2

    return row_to_predict


def make_predictions(league: str, league_model, league_data: pd.DataFrame, competitions: dict):
    global league_predictions
    league_predictions = {}
    
    # فلترة مباريات اليوم فقط
    today = datetime.now().strftime('%Y-%m-%d')
    if league in competitions and 'next_matches' in competitions[league]:
        filtered_matches = []
        for match in competitions[league]['next_matches']:
            match_date = match['date'].split('T')[0]
            if match_date == today:
                filtered_matches.append(match)
        competitions[league]['next_matches'] = filtered_matches
    
    # إذا لم تكن هناك مباريات اليوم، نرجع رسالة فارغة
    if not competitions.get(league, {}).get('next_matches', []):
        return ""
        
    # تحضير التنبؤات للمباريات
    league_name = competitions[league]['name']
    league_predictions[league_name] = []
    
    """Makes predictions for a specific league and formats them into a Telegram message.
    
    Args:
        league (str): The league identifier.
        league_model: The machine learning model for the league.
        league_data (pd.DataFrame): DataFrame containing the league data.
        competitions (dict): Dictionary containing competition details and upcoming matches.
    
    Returns:
        str: A formatted string containing the predictions for the league.
    """
    league_section = ""
    for competition_league, competitions_info in competitions.items():
        if competition_league == league:
            league_section = f"**{competitions_info['name']}**:\n"
            for match in competitions_info["next_matches"]:
                home_team = match['home_team']
                away_team = match['away_team']

                if home_team not in league_data['HomeTeam'].values or away_team not in league_data['AwayTeam'].values:
                    continue

                home_team_df = league_data[league_data['HomeTeam'] == home_team]
                away_team_df = league_data[league_data['AwayTeam'] == away_team]

                numeric_columns = league_data.select_dtypes(include=['number']).columns
                if 'Over2.5' in numeric_columns:
                    numeric_columns = numeric_columns.drop('Over2.5')

                row_to_predict = prepare_row_to_predict(home_team_df, away_team_df, numeric_columns)
                X_test = row_to_predict.values
                prediction = league_model.predict(X_test)
                predicted_probability = league_model.predict_proba(X_test)[0]

                confidence = predicted_probability[1] * 100
                if prediction == 1:
                    result = f"Over 2.5 Goals! 🔥 ({round(confidence, 1)}%)"
                    prediction_type = 'over'
                else:
                    result = f"Under 2.5 Goals ❄️ ({round(100 - confidence, 1)}%)"
                    prediction_type = 'under'
                
                # إضافة التنبؤ إلى قائمة المباريات
                if league not in league_predictions:
                    league_predictions[league] = []
                
                match_prediction = {
                    'home': home_team,
                    'away': away_team,
                    'prediction': prediction_type,
                    'confidence': round(confidence if prediction == 1 else 100 - confidence, 1)
                }
                league_predictions[league].append(match_prediction)

                league_section += f"- ⚽ **{home_team}** 🆚 **{away_team}**: {result}\n"

    return league_section


def save_predictions(predictions: dict, output_file: str):
    """Save predictions to a JSON file.

    Args:
        predictions (dict): Dictionary containing predictions.
        output_file (str): Path to the output JSON file.
    """
    output = {
        "timestamp": datetime.now().isoformat(),
        "predictions": predictions
    }
    
    # حفظ التنبؤات
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(output, f, ensure_ascii=False, indent=4)
    
    # نسخ الملف إلى مجلد web
    web_dir = os.path.join(os.path.dirname(output_file), 'web')
    os.makedirs(web_dir, exist_ok=True)
    web_output = os.path.join(web_dir, os.path.basename(output_file))
    shutil.copy2(output_file, web_output)


def main(models_dir: str, data_dir: str, final_predictions_out_file: str, next_matches_file: str):
    """Main function to generate predictions.

    Args:
        models_dir (str): Directory containing trained models.
        data_dir (str): Directory containing preprocessed data.
        final_predictions_out_file (str): Path to save final predictions.
        next_matches_file (str): Path to the next matches JSON file.
    """
    print("Loading JSON file with upcoming matches...\n")
    with open(next_matches_file, 'r', encoding='utf-8') as file:
        competitions = json.load(file)

    all_predictions = {}
    for league in competitions:
        print("----------------------------------\n")
        print(f"Making predictions for {league}...\n")
        
        # تحميل النموذج والبيانات
        model_path = os.path.join(models_dir, f"{league}_stacking_classifier.pkl")
        data_path = os.path.join(data_dir, f"{league}_merged_preprocessed.csv")
        
        if not os.path.exists(model_path) or not os.path.exists(data_path):
            print(f"Missing model or data for {league}. Skipping...")
            continue

        try:
            league_model = load_model(model_path)
            print(f"Loading data from {data_path}...")
            league_data = load_league_data(data_path)
            print(f"Loaded model and data for {league}.")
            print(f"Predicting matches for {league}...")
            
            # عمل التنبؤات للدوري
            league_predictions = make_predictions(league, league_model, league_data, competitions)
            if league_predictions:
                all_predictions[competitions[league]['name']] = league_predictions
            print(f"Predictions made for {league}.")
        except Exception as e:
            print(f"Error processing {league}: {e}")
            continue
    
    # حفظ التنبؤات
    if all_predictions:
        save_predictions(all_predictions, final_predictions_out_file)
        print(f"\nPredictions saved to {final_predictions_out_file}")
    else:
        print("\nNo predictions were generated.")
    league_categories = {
        'major_european': ['E0', 'SP1', 'I1', 'D1', 'F1'],
        'arab': ['SAU', 'EGY', 'UAE', 'QAT', 'TUN', 'MAR'],
        'other_european': ['N1'],
        'asian': ['JPN', 'KOR', 'CHN']
    }
    
    league_names = {
        'E0': 'الدوري الإنجليزي الممتاز',
        'SP1': 'الدوري الإسباني',
        'I1': 'الدوري الإيطالي',
        'D1': 'الدوري الألماني',
        'F1': 'الدوري الفرنسي',
        'SAU': 'الدوري السعودي',
        'EGY': 'الدوري المصري',
        'UAE': 'الدوري الإماراتي',
        'QAT': 'الدوري القطري',
        'TUN': 'الدوري التونسي',
        'MAR': 'الدوري المغربي',
        'N1': 'الدوري الهولندي',
        'JPN': 'الدوري الياباني',
        'KOR': 'الدوري الكوري',
        'CHN': 'الدوري الصيني'
    }
    
    # تنظيم التنبؤات حسب الفئات
    for category, leagues in league_categories.items():
        for league in leagues:
            if league in league_predictions:
                web_predictions['leagues'][category][league] = {
                    'name': league_names.get(league, league),
                    'matches': league_predictions[league]
                }
    
    # حفظ التنبؤات في ملف JSON
    web_predictions_file = 'web/predictions.json'
    with open(web_predictions_file, 'w', encoding='utf-8') as f:
        json.dump(web_predictions, f, ensure_ascii=False, indent=4)
        print(f"تم حفظ التنبؤات للواجهة في {web_predictions_file}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="AI Football Predictions Script")
    parser.add_argument('--input_leagues_models_dir', type=str, required=True, help="Directory containing the model files")
    parser.add_argument('--input_data_predict_dir', type=str, required=True, help="Directory containing the processed data files")
    parser.add_argument('--final_predictions_out_file', type=str, required=True, help="File path to save the Telegram message output")
    parser.add_argument('--next_matches', type=str, required=True, help="Path to the JSON file with upcoming matches information")

    args = parser.parse_args()
    main(args.input_leagues_models_dir, args.input_data_predict_dir, args.final_predictions_out_file, args.next_matches)
