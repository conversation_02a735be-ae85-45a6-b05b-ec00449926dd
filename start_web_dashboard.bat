@echo off
chcp 65001 > nul
title نظام توقعات كرة القدم - لوحة التحكم الويب

echo.
echo ========================================
echo    🚀 نظام توقعات كرة القدم بالذكاء الاصطناعي
echo    📊 لوحة التحكم الويب المتقدمة
echo ========================================
echo.

echo 📋 فحص المتطلبات...

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت. يرجى تثبيت Python أولاً.
    pause
    exit /b 1
)

echo ✅ Python مثبت

REM Check if pip is available
pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ pip غير متوفر. يرجى تثبيت pip أولاً.
    pause
    exit /b 1
)

echo ✅ pip متوفر

REM Install required packages
echo.
echo 📦 تثبيت المكتبات المطلوبة...
pip install flask flask-cors >nul 2>&1
if errorlevel 1 (
    echo ⚠️  تحذير: قد تكون بعض المكتبات غير مثبتة بشكل صحيح
) else (
    echo ✅ تم تثبيت المكتبات بنجاح
)

REM Check if data directory exists
if not exist "data" (
    echo 📁 إنشاء مجلد البيانات...
    mkdir data
)

REM Check if models directory exists
if not exist "models" (
    echo 🤖 إنشاء مجلد النماذج...
    mkdir models
)

REM Check if web directory exists
if not exist "web" (
    echo 🌐 مجلد الويب غير موجود!
    pause
    exit /b 1
)

echo.
echo 🌐 بدء تشغيل خادم الويب...
echo.
echo 📍 الروابط المتاحة:
echo    🏠 الصفحة الرئيسية: http://localhost:5000
echo    📊 لوحة التحكم: http://localhost:5000/dashboard.html
echo    📈 التنبؤات: http://localhost:5000/predictions
echo.
echo 💡 نصائح:
echo    • استخدم Ctrl+C لإيقاف الخادم
echo    • تأكد من أن المنفذ 5000 غير مستخدم
echo    • يمكنك الوصول للموقع من أي جهاز في الشبكة المحلية
echo.
echo ⏳ جاري بدء التشغيل...
echo.

REM Start the web server
python scripts\web_server.py

echo.
echo 🛑 تم إيقاف الخادم
pause
