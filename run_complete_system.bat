@echo off
chcp 65001 > nul
title نظام توقعات كرة القدم - التشغيل الشامل

echo.
echo ========================================
echo    🚀 نظام توقعات كرة القدم بالذكاء الاصطناعي
echo    📊 التشغيل الشامل للنظام
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت. يرجى تثبيت Python أولاً.
    pause
    exit /b 1
)

echo ✅ Python مثبت

REM Create necessary directories
echo 📁 إنشاء المجلدات المطلوبة...
if not exist "data" mkdir data
if not exist "models" mkdir models
if not exist "logs" mkdir logs
if not exist "config" mkdir config
if not exist "web" mkdir web

echo ✅ تم إنشاء المجلدات

REM Install requirements
echo 📦 تثبيت المكتبات المطلوبة...
pip install -r requirements.txt
if errorlevel 1 (
    echo ⚠️  تحذير: قد تكون بعض المكتبات غير مثبتة بشكل صحيح
    echo 🔄 محاولة تثبيت المكتبات الأساسية...
    pip install flask flask-cors pandas numpy scikit-learn
)

echo ✅ تم تثبيت المكتبات

echo.
echo 🎯 اختر العملية المطلوبة:
echo.
echo [1] تشغيل النظام الكامل (تحديث + تنبؤات + واجهة ويب)
echo [2] تشغيل واجهة الويب فقط
echo [3] تحديث البيانات والتنبؤات فقط
echo [4] تدريب النماذج من جديد
echo [5] التحديث اليومي السريع
echo [0] خروج
echo.

set /p choice="أدخل اختيارك (0-5): "

if "%choice%"=="0" goto :end
if "%choice%"=="1" goto :full_system
if "%choice%"=="2" goto :web_only
if "%choice%"=="3" goto :data_only
if "%choice%"=="4" goto :retrain
if "%choice%"=="5" goto :daily_update

echo ❌ اختيار غير صحيح
goto :end

:full_system
echo.
echo 🚀 تشغيل النظام الكامل...
echo.

echo 📋 الخطوة 1/5: تحميل البيانات التاريخية...
python scripts\data_acquisition.py
if errorlevel 1 (
    echo ⚠️  تحذير: فشل في تحميل بعض البيانات
)

echo 📋 الخطوة 2/5: معالجة البيانات...
python scripts\data_preprocessing.py
if errorlevel 1 (
    echo ❌ فشل في معالجة البيانات
    goto :error
)

echo 📋 الخطوة 3/5: تدريب النماذج...
python scripts\train_models.py
if errorlevel 1 (
    echo ❌ فشل في تدريب النماذج
    goto :error
)

echo 📋 الخطوة 4/5: تحديث المباريات القادمة...
python scripts\acquire_next_matches.py --output_file data\next_matches.json
if errorlevel 1 (
    echo ⚠️  تحذير: فشل في تحديث المباريات
)

echo 📋 الخطوة 5/5: توليد التنبؤات...
python scripts\make_predictions.py
if errorlevel 1 (
    echo ❌ فشل في توليد التنبؤات
    goto :error
)

echo ✅ تم إعداد النظام بنجاح!
echo.
echo 🌐 بدء تشغيل واجهة الويب...
goto :start_web

:web_only
echo.
echo 🌐 تشغيل واجهة الويب فقط...
goto :start_web

:data_only
echo.
echo 📊 تحديث البيانات والتنبؤات...
echo.

echo 📋 تحديث المباريات القادمة...
python scripts\acquire_next_matches.py --output_file data\next_matches.json

echo 📋 توليد التنبؤات...
python scripts\make_predictions.py

echo ✅ تم تحديث البيانات والتنبؤات
goto :end

:retrain
echo.
echo 🤖 إعادة تدريب النماذج...
echo.

echo 📋 تحميل أحدث البيانات...
python scripts\data_acquisition.py

echo 📋 معالجة البيانات...
python scripts\data_preprocessing.py

echo 📋 تدريب النماذج الجديدة...
python scripts\train_models.py

echo ✅ تم إعادة تدريب النماذج بنجاح
goto :end

:daily_update
echo.
echo 🔄 التحديث اليومي السريع...
python scripts\daily_web_update.py
goto :end

:start_web
echo.
echo 🌐 بدء تشغيل خادم الويب...
echo.
echo 📍 الروابط المتاحة:
echo    🏠 الصفحة الرئيسية: http://localhost:5000
echo    📊 لوحة التحكم: http://localhost:5000/dashboard.html
echo    📈 التنبؤات: http://localhost:5000/predictions
echo.
echo 💡 نصائح:
echo    • استخدم Ctrl+C لإيقاف الخادم
echo    • يمكنك الوصول للموقع من أي جهاز في الشبكة المحلية
echo    • تأكد من أن المنفذ 5000 غير مستخدم
echo.
echo ⏳ جاري بدء التشغيل...
echo.

python scripts\web_server.py
goto :end

:error
echo.
echo ❌ حدث خطأ أثناء تشغيل النظام
echo 📝 تحقق من ملفات السجل في مجلد logs
echo 🔄 جرب تشغيل الخطوات منفردة لتحديد المشكلة
echo.
pause
exit /b 1

:end
echo.
echo 🛑 انتهى تشغيل النظام
pause
