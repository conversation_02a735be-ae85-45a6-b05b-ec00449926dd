"""
Script to gather and merge football match data from multiple leagues and seasons.

Usage:
------
Run this script from the terminal in the root folder as follows:

    python scripts/data_acquisition.py --leagues E0 I1 SP1 F1 D1 --seasons 2425 2324 2223 --raw_data_output_dir data/raw

Parameters:
-----------
--leagues : str
    A space-separated list of league acronyms (e.g., E0 I1 SP1).
--seasons : str
    A space-separated list of season codes (e.g., 2324 2223).
--raw_data_output_dir : str
    Directory where the merged CSV files will be saved.

This script will download the corresponding CSV files from football-data.co.uk,
merge them by league, and save the resulting files in the specified output directory.
"""

import os
import argparse
import pandas as pd
import requests
from datetime import datetime
import time

# API Football Configuration
API_FOOTBALL_KEY = 'a76425798e481275c75572b8d37a40d1'
API_FOOTBALL_HOST = 'v3.football.api-sports.io'

# League IDs for API-Football
API_FOOTBALL_LEAGUES = {
    # الدوريات الإنجليزية
    'E0': 39,    # Premier League
    'E1': 40,    # Championship
    'E2': 41,    # League One
    'E3': 42,    # League Two
    'EC': 43,    # National League

    # الدوريات الاسكتلندية
    'SC0': 179,  # Premiership
    'SC1': 180,  # Championship
    'SC2': 181,  # League One
    'SC3': 182,  # League Two

    # الدوريات الأوروبية الكبرى
    'I1': 135,   # Serie A
    'I2': 136,   # Serie B
    'D1': 78,    # Bundesliga
    'D2': 79,    # 2. Bundesliga
    'SP1': 140,  # La Liga
    'SP2': 141,  # La Liga 2
    'F1': 61,    # Ligue 1
    'F2': 62,    # Ligue 2

    # دوريات أوروبية أخرى
    'N1': 88,    # Eredivisie
    'P1': 94,    # Primeira Liga
    'B1': 144,   # Jupiler Pro League
    'T1': 203,   # Super Lig
    'G1': 197,   # Super League

    # دوريات أمريكا الجنوبية
    'ARG': 128,  # Argentina Liga Profesional
    'BRA': 71,   # Brasileirão
    'URY': 268,  # Primera División Uruguay
    'CHL': 265,  # Primera División Chile
    'COL': 239,  # Primera A Colombia

    # دوريات أمريكا الشمالية والوسطى
    'MEX': 262,  # Liga MX
    'USA': 253,  # MLS
    'CAN': 254,  # Canadian Premier League

    # دوريات آسيوية
    'JPN': 98,   # J1 League
    'KOR': 292,  # K League 1
    'CHN': 169,  # Super League
    'SAU': 307,  # Saudi Pro League
    'QAT': 305,  # Qatar Stars League
    'UAE': 297,  # UAE Pro League

    # دوريات أفريقية
    'EGY': 233,  # Egyptian Premier League
    'MAR': 200,  # Botola Pro
    'TUN': 202,  # Ligue 1
    'RSA': 288,  # Premier Soccer League

    # دوريات أوقيانوسيا
    'AUS': 188,  # A-League
    'NZL': 205,  # New Zealand Football Championship
}

# Valid league acronyms (now includes all available leagues)
VALID_LEAGUES = [
    # English Leagues
    "E0", "E1", "E2", "E3", "EC",
    # Scottish Leagues
    "SC0", "SC1", "SC2", "SC3",
    # Major European Leagues
    "I1", "I2",  # Italy
    "D1", "D2",  # Germany
    "SP1", "SP2",  # Spain
    "F1", "F2",  # France
    # Other European Leagues
    "N1",  # Netherlands
    "P1",  # Portugal
    "B1",  # Belgium
    "T1",  # Turkey
    "G1",  # Greece
    # South American Leagues
    "ARG",  # Argentina
    "BRA",  # Brazil
    "URY",  # Uruguay
    "CHL",  # Chile
    "COL",  # Colombia
    # North & Central American Leagues
    "MEX",  # Mexico
    "USA",  # USA
    "CAN",  # Canada
    # Asian Leagues
    "JPN",  # Japan
    "KOR",  # South Korea
    "CHN",  # China
    "SAU",  # Saudi Arabia
    "QAT",  # Qatar
    "UAE",  # UAE
    # African Leagues
    "EGY",  # Egypt
    "MAR",  # Morocco
    "TUN",  # Tunisia
    "RSA",  # South Africa
    # Oceania Leagues
    "AUS",  # Australia
    "NZL"   # New Zealand
]

# Valid season codes (limiting to recent years only)
VALID_SEASONS = ["2425","2324", "2223", "2122", "2021"]

def validate_leagues(leagues):
    """
    Validates the list of league acronyms.

    Parameters
    ----------
    leagues : list of str
        List of league acronyms to validate.
    
    Raises
    ------
    ValueError
        If any of the league acronyms are not valid.
    """
    for league in leagues:
        if league not in VALID_LEAGUES:
            raise ValueError(f"Invalid league acronym: {league}. Allowed values are {', '.join(VALID_LEAGUES)}")

def validate_seasons(seasons):
    """
    Validates the list of season codes.

    Parameters
    ----------
    seasons : list of str
        List of season codes to validate.
    
    Raises
    ------
    ValueError
        If any of the season codes are not valid.
    """
    for season in seasons:
        if season not in VALID_SEASONS:
            raise ValueError(f"Invalid season code: {season}. Allowed values are {', '.join(VALID_SEASONS)}")

def download_from_api_football(league_code, season, raw_data_output_dir):
    """
    Downloads football match data from API-Football for a specific league and season.

    Parameters
    ----------
    league_code : str
        League code (e.g., 'ARG', 'BRA', 'MEX')
    season : str
        Season code (e.g., '2324')
    raw_data_output_dir : str
        Directory where the CSV files will be saved.

    Returns
    -------
    pandas.DataFrame or None
        DataFrame containing the match data, or None if download failed.
    """
    if league_code not in API_FOOTBALL_LEAGUES:
        print(f"League {league_code} not found in API Football configuration")
        return None

    # Convert season code to full year
    if len(season) == 4:
        year = '20' + season[:2]
    else:
        year = season

    headers = {
        'x-rapidapi-key': API_FOOTBALL_KEY,
        'x-rapidapi-host': API_FOOTBALL_HOST
    }

    url = f"https://{API_FOOTBALL_HOST}/fixtures"
    params = {
        'league': API_FOOTBALL_LEAGUES[league_code],
        'season': year
    }

    try:
        response = requests.get(url, headers=headers, params=params)
        response.raise_for_status()
        data = response.json()

        if data['results'] == 0:
            print(f"No data found for {league_code} season {season}")
            return None

        # Convert API response to DataFrame
        matches = []
        for fixture in data['response']:
            match = {
                'Date': fixture['fixture']['date'][:10],
                'HomeTeam': fixture['teams']['home']['name'],
                'AwayTeam': fixture['teams']['away']['name'],
                'FTHG': fixture['score']['fulltime']['home'],
                'FTAG': fixture['score']['fulltime']['away'],
                'FTR': 'H' if fixture['teams']['home']['winner'] else 'A' if fixture['teams']['away']['winner'] else 'D'
            }
            matches.append(match)

        df = pd.DataFrame(matches)
        print(f"Downloaded data from API-Football for {league_code} season {season}")
        return df

    except Exception as e:
        print(f"Failed to download data from API-Football for {league_code} season {season}: {e}")
        return None

def download_and_merge_data(leagues, seasons, raw_data_output_dir):
    """
    Downloads and merges football match data from the specified leagues and seasons,
    keeping only the common columns and preserving their order.

    Parameters
    ----------
    leagues : list of str
        List of league acronyms (e.g., ["E0", "I1", "SP1"]).
    seasons : list of str
        List of season codes (e.g., ["2324", "2223"]).
    raw_data_output_dir : str
        Directory where the merged CSV files will be saved.
    
    Returns
    -------
    None
    """
    os.makedirs(raw_data_output_dir, exist_ok=True)
    
    for league in leagues:
        league_dfs = []
        common_columns = None
        column_order = []
        
        # Check if this is an API-Football league
        is_api_league = league in API_FOOTBALL_LEAGUES

        for season in seasons:
            try:
                if is_api_league:
                    df = download_from_api_football(league, season, raw_data_output_dir)
                else:
                    url = f"https://www.football-data.co.uk/mmz4281/{season}/{league}.csv"
                    df = pd.read_csv(url)
                    print(f"Downloaded data from {url}")
                
                if df is not None:
                    # Determine the common columns across all DataFrames
                    if common_columns is None:
                        common_columns = set(df.columns)
                        column_order = df.columns.tolist()  # Preserve initial column order
                    else:
                        common_columns.intersection_update(df.columns)
                    
                    league_dfs.append(df)
                    
            except Exception as e:
                print(f"Failed to download data: {e}")
                continue
                
                league_dfs.append(df)
                
            except Exception as e:
                print(f"Failed to download data from {url}: {e}")
                continue
        
        if league_dfs and common_columns:
            # Keep only the common columns, preserving the order from the first DataFrame
            common_columns = [col for col in column_order if col in common_columns]
            league_dfs = [df[common_columns] for df in league_dfs]
            
            # Concatenate the DataFrames
            merged_df = pd.concat(league_dfs, ignore_index=True)
            
            # Save to CSV
            output_path = os.path.join(raw_data_output_dir, f"{league}_merged.csv")
            merged_df.to_csv(output_path, index=False)
            print(f"Saved merged data to {output_path}")



def parse_arguments():
    """
    Parses command-line arguments.

    Returns
    -------
    args : argparse.Namespace
        Parsed command-line arguments.
    """
    parser = argparse.ArgumentParser(description="Download and merge football data from multiple leagues and seasons.")
    
    parser.add_argument(
        "--leagues", 
        nargs="+", 
        required=True, 
        help="A list of league acronyms (e.g., E0 I1 SP1)."
    )
    
    parser.add_argument(
        "--seasons", 
        nargs="+", 
        required=True, 
        help="A list of season codes (e.g., 2324 2223)."
    )
    
    parser.add_argument(
        "--raw_data_output_dir", 
        type=str, 
        required=True, 
        help="Directory where the merged CSV files will be saved."
    )
    
    return parser.parse_args()

if __name__ == "__main__":
    args = parse_arguments()
    
    # Validate leagues and seasons
    validate_leagues(args.leagues)
    validate_seasons(args.seasons)
    
    # Download and merge data
    download_and_merge_data(
        leagues=args.leagues, 
        seasons=args.seasons,
        raw_data_output_dir=args.raw_data_output_dir
    )
