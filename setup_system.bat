@echo off
echo ======================================
echo Football Predictions System Setup
echo ======================================

cd /d %~dp0

echo.
echo 1. Creating required directories...
mkdir data 2>nul
mkdir data\raw 2>nul
mkdir data\processed 2>nul
mkdir models 2>nul
mkdir web 2>nul

echo.
echo 2. Installing required Python packages...
pip install pandas numpy scikit-learn requests

echo.
echo 3. Downloading historical data...
python scripts/data_acquisition.py --output_dir data/raw

echo.
echo 4. Processing data...
python scripts/data_processing.py --input_dir data/raw --output_dir data/processed

echo.
echo 5. Training prediction models...
python scripts/train_models.py --input_dir data/processed --output_dir models

echo.
echo 6. Getting initial matches data...
python scripts/update_matches.py --output_file data/next_matches.json

echo.
echo 7. Making initial predictions...
python scripts/make_predictions.py --input_leagues_models_dir models --input_data_predict_dir data/processed --final_predictions_out_file data/final_predictions.txt --next_matches data/next_matches.json

echo.
echo ======================================
echo Setup completed successfully!
echo ======================================
echo.
echo You can now run start_system.bat to start the prediction system
echo or update_predictions.bat to update predictions only.
echo.
pause
