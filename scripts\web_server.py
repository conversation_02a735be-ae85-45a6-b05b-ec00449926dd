#!/usr/bin/env python3
"""
Advanced Web Server for AI Football Predictions Dashboard
Provides REST API endpoints for the web interface
"""

import os
import sys
import json
import subprocess
import threading
import time
from datetime import datetime
from flask import Flask, jsonify, request, send_from_directory
from flask_cors import CORS
import logging

# Add the scripts directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

app = Flask(__name__)
CORS(app)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FootballPredictionServer:
    def __init__(self):
        self.base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        self.scripts_dir = os.path.join(self.base_dir, 'scripts')
        self.web_dir = os.path.join(self.base_dir, 'web')
        self.data_dir = os.path.join(self.base_dir, 'data')
        self.models_dir = os.path.join(self.base_dir, 'models')
        
        # Process tracking
        self.running_processes = {}
        self.process_logs = {}
        
        # Ensure directories exist
        os.makedirs(self.data_dir, exist_ok=True)
        os.makedirs(self.models_dir, exist_ok=True)
        
    def run_script(self, script_name, args=None):
        """Run a Python script and track its progress"""
        if args is None:
            args = []
            
        script_path = os.path.join(self.scripts_dir, f"{script_name}.py")
        
        if not os.path.exists(script_path):
            return {"success": False, "error": f"Script {script_name} not found"}
        
        try:
            # Create command
            cmd = [sys.executable, script_path] + args
            
            # Start process
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                cwd=self.base_dir
            )
            
            # Track process
            process_id = f"{script_name}_{int(time.time())}"
            self.running_processes[process_id] = process
            self.process_logs[process_id] = []
            
            # Start log monitoring thread
            threading.Thread(
                target=self._monitor_process,
                args=(process_id, process),
                daemon=True
            ).start()
            
            return {
                "success": True,
                "process_id": process_id,
                "message": f"Started {script_name}"
            }
            
        except Exception as e:
            logger.error(f"Error running script {script_name}: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def _monitor_process(self, process_id, process):
        """Monitor a running process and collect logs"""
        try:
            while process.poll() is None:
                output = process.stdout.readline()
                if output:
                    self.process_logs[process_id].append({
                        "timestamp": datetime.now().isoformat(),
                        "message": output.strip(),
                        "type": "info"
                    })
                time.sleep(0.1)
            
            # Get final output
            stdout, stderr = process.communicate()
            
            if stdout:
                for line in stdout.strip().split('\n'):
                    if line:
                        self.process_logs[process_id].append({
                            "timestamp": datetime.now().isoformat(),
                            "message": line,
                            "type": "info"
                        })
            
            if stderr:
                for line in stderr.strip().split('\n'):
                    if line:
                        self.process_logs[process_id].append({
                            "timestamp": datetime.now().isoformat(),
                            "message": line,
                            "type": "error"
                        })
            
            # Mark process as completed
            if process.returncode == 0:
                self.process_logs[process_id].append({
                    "timestamp": datetime.now().isoformat(),
                    "message": f"Process {process_id} completed successfully",
                    "type": "success"
                })
            else:
                self.process_logs[process_id].append({
                    "timestamp": datetime.now().isoformat(),
                    "message": f"Process {process_id} failed with code {process.returncode}",
                    "type": "error"
                })
            
            # Clean up
            if process_id in self.running_processes:
                del self.running_processes[process_id]
                
        except Exception as e:
            logger.error(f"Error monitoring process {process_id}: {str(e)}")
            self.process_logs[process_id].append({
                "timestamp": datetime.now().isoformat(),
                "message": f"Monitoring error: {str(e)}",
                "type": "error"
            })
    
    def get_predictions(self):
        """Load current predictions"""
        predictions_file = os.path.join(self.web_dir, 'predictions.json')
        
        if os.path.exists(predictions_file):
            try:
                with open(predictions_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"Error loading predictions: {str(e)}")
                return {}
        return {}
    
    def get_system_stats(self):
        """Get system statistics"""
        predictions = self.get_predictions()
        
        total_matches = 0
        total_leagues = len(predictions)
        
        for league_data in predictions.values():
            if isinstance(league_data, dict) and 'next_matches' in league_data:
                total_matches += len(league_data['next_matches'])
        
        # Check if models exist
        models_exist = os.path.exists(self.models_dir) and len(os.listdir(self.models_dir)) > 0
        
        # Get last update time
        predictions_file = os.path.join(self.web_dir, 'predictions.json')
        last_update = None
        if os.path.exists(predictions_file):
            last_update = datetime.fromtimestamp(os.path.getmtime(predictions_file)).isoformat()
        
        return {
            "total_matches": total_matches,
            "total_leagues": total_leagues,
            "models_exist": models_exist,
            "last_update": last_update,
            "running_processes": len(self.running_processes)
        }

# Initialize server
server = FootballPredictionServer()

# API Routes
@app.route('/')
def index():
    """Serve the dashboard"""
    return send_from_directory(server.web_dir, 'dashboard.html')

@app.route('/<path:filename>')
def serve_static(filename):
    """Serve static files"""
    return send_from_directory(server.web_dir, filename)

@app.route('/api/predictions')
def get_predictions():
    """Get current predictions"""
    try:
        predictions = server.get_predictions()
        return jsonify({
            "success": True,
            "data": predictions
        })
    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/api/stats')
def get_stats():
    """Get system statistics"""
    try:
        stats = server.get_system_stats()
        return jsonify({
            "success": True,
            "data": stats
        })
    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/api/update-matches', methods=['POST'])
def update_matches():
    """Update matches from API"""
    try:
        result = server.run_script('acquire_next_matches', [
            '--output_file', os.path.join(server.data_dir, 'next_matches.json')
        ])
        return jsonify(result)
    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/api/make-predictions', methods=['POST'])
def make_predictions():
    """Generate predictions"""
    try:
        result = server.run_script('make_predictions')
        return jsonify(result)
    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/api/retrain-models', methods=['POST'])
def retrain_models():
    """Retrain ML models"""
    try:
        result = server.run_script('train_models')
        return jsonify(result)
    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/api/download-data', methods=['POST'])
def download_data():
    """Download historical data"""
    try:
        result = server.run_script('data_acquisition')
        return jsonify(result)
    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/api/preprocess-data', methods=['POST'])
def preprocess_data():
    """Preprocess data"""
    try:
        result = server.run_script('data_preprocessing')
        return jsonify(result)
    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/api/process-status/<process_id>')
def get_process_status(process_id):
    """Get status of a running process"""
    try:
        is_running = process_id in server.running_processes
        logs = server.process_logs.get(process_id, [])
        
        return jsonify({
            "success": True,
            "data": {
                "running": is_running,
                "logs": logs[-50:]  # Return last 50 log entries
            }
        })
    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/api/logs')
def get_all_logs():
    """Get all process logs"""
    try:
        all_logs = []
        for process_id, logs in server.process_logs.items():
            all_logs.extend(logs)
        
        # Sort by timestamp
        all_logs.sort(key=lambda x: x['timestamp'], reverse=True)
        
        return jsonify({
            "success": True,
            "data": all_logs[:100]  # Return last 100 entries
        })
    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/api/full-pipeline', methods=['POST'])
def run_full_pipeline():
    """Run the complete pipeline"""
    try:
        # This will run all steps in sequence
        steps = [
            ('acquire_next_matches', ['--output_file', os.path.join(server.data_dir, 'next_matches.json')]),
            ('make_predictions', [])
        ]
        
        results = []
        for script_name, args in steps:
            result = server.run_script(script_name, args)
            results.append(result)
            if not result['success']:
                break
        
        return jsonify({
            "success": all(r['success'] for r in results),
            "results": results
        })
    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

if __name__ == '__main__':
    print("🚀 Starting AI Football Predictions Web Server...")
    print(f"📁 Base directory: {server.base_dir}")
    print(f"🌐 Web interface: http://localhost:5000")
    print(f"📊 Dashboard: http://localhost:5000/dashboard.html")
    
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True,
        threaded=True
    )
