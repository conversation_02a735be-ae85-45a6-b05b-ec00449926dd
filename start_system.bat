@echo off
echo ======================================
echo Starting Football Predictions System
echo ======================================

cd /d %~dp0

echo.
echo 1. Updating matches data...
python scripts/update_matches.py --output_file data/next_matches.json

echo.
echo 2. Making predictions...
python scripts/make_predictions.py --input_leagues_models_dir models --input_data_predict_dir data/processed --final_predictions_out_file data/final_predictions.txt --next_matches data/next_matches.json

echo.
echo 3. Starting web server...
start /B python scripts/server.py

echo.
echo System started successfully! The web interface is available at:
echo http://localhost:8000

pause
