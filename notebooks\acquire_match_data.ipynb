{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["### Data Preprocessing\n", "\n", "### Overview\n", "\n", "This notebook is used to gather football's matches information (Home and Away) teams to be able to make predictions.  \n", "\n", "### Pre-requisites \n", "\n", "1. A conda environment is needed.\n", "\n", "For example:\n", "```\n", "cd path/to/conda/dir\n", "conda env create -f aifootball_predictions.yaml\n", "conda activate aifootball_predictions\n", "python -m ipykernel install --user --name aifootball_predictions --display-name \"aifootball_predictions\"\n", "```\n", "\n", "2. A `~/.env` file\n", "\n", "To create it, from the terminal:\n", "\n", "`vim ~/.env`\n", "\n", "then press `i` on the key board to insert lines into the file. Once you're happy with your changes press esc and then type `:wq!`.\n", "\n", "A `~/.env` file is necessary to store the `API_FOOTBALL_DATA`. For example:\n", "\n", "OPENAI_API_KEY=sdHbc..&66dc\n", "\n", "You can get your own API key [here](https://www.football-data.org/)\n", "\n", "### Authors\n", "\n", "- <EMAIL>"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# install the necessary packages\n", "from dotenv import load_dotenv\n", "import requests\n", "import pandas as pd\n", "from datetime import datetime\n", "import os\n", "import json\n", "from fuzzywuzzy import process\n", "from PIL import Image\n", "from io import BytesIO"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["load_dotenv(dotenv_path=os.path.expanduser(\"~/.env\"))"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# Define useful variables\n", "API_KEY = os.getenv(\"API_FOOTBALL_DATA\")\n", "BASE_URL = 'https://api.football-data.org/v4'\n", "headers = { 'X-Auth-Token': API_KEY }\n", "TEST_URL = 'https://api.football-data.org/v4/competitions/2021/matches'"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["response = requests.get(TEST_URL, headers=headers)\n", "data = response.json()  # Convert response to Python dictionary\n", "with open('competitions.json', 'w') as json_file:\n", "    json.dump(data, json_file, indent=4)\n"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [], "source": ["# Dictionary of major competition IDs (you can expand this list)\n", "COMPETITIONS = {\n", "    'E0': {\n", "        'id': 2021, \n", "        'crest': 'https://crests.football-data.org/PL.png', \n", "        'name': 'Premier League', \n", "        'next_matches': []\n", "    },\n", "    'SP1': {\n", "        'id': 2014, \n", "        'crest': 'https://crests.football-data.org/PD.png', \n", "        'name': 'La Liga', \n", "        'next_matches': []\n", "    },\n", "    'I1': {\n", "        'id': 2019, \n", "        'crest': 'https://crests.football-data.org/SA.png', \n", "        'name': 'Serie A', \n", "        'next_matches': []\n", "    },\n", "    'D1': {\n", "        'id': 2002, \n", "        'crest': 'https://crests.football-data.org/BL1.png', \n", "        'name': 'Bundesliga', \n", "        'next_matches': []\n", "    },\n", "    'F1': {\n", "        'id': 2015, \n", "        'crest': 'https://crests.football-data.org/FL1.png', \n", "        'name': 'Ligue 1', \n", "        'next_matches': []\n", "    }\n", "}\n", "\n"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [], "source": ["def get_next_matches():\n", "    \"\"\"Get the next matches for each major league\"\"\"\n", "\n", "    # Iterate over the competitions\n", "    for competition, competition_info in COMPETITIONS.items():\n", "\n", "        url = f'{BASE_URL}/competitions/{competition_info[\"id\"]}/matches'\n", "        response = requests.get(url, headers=headers)\n", "        data = response.json()\n", "\n", "        # Get the currentMatchday\n", "        current_matchday = data['matches'][0]['season']['currentMatchday'] #int \n", "        # Get the total number of matches\n", "        total_number_of_matches = len(data['matches']) #int\n", "        # Get the last match_day to avoid out of range error\n", "        last_match_day = data['matches'][-1]['matchday']\n", "        # Define the next matchday\n", "        next_matchday = current_matchday +1 if current_matchday < last_match_day else last_match_day\n", "\n", "        print(f'{competition}: Current Matchday {current_matchday}, Total Matches {total_number_of_matches}')  \n", "\n", "        # Iterate over all the matches\n", "        for match in data['matches']:\n", "            # Select only the matches for the current matchday\n", "            if match['matchday'] != next_matchday:\n", "                continue\n", "            # Get the match date and time\n", "            match_date = match['utcDate']\n", "            match_date = datetime.strptime(match_date, '%Y-%m-%dT%H:%M:%SZ')\n", "            # Format the date as 'YYYY-MM-DD HH:MM:SS'\n", "            formatted_date = match_date.strftime('%Y-%m-%d %H:%M:%S')\n", "            # Get the home team and away team\n", "            home_team = match['homeTeam']['name']\n", "            away_team = match['awayTeam']['name']\n", "            \n", "            # Get the crest for the home team and away team\n", "            home_team_crest_url = match['homeTeam']['crest']\n", "            away_team_crest_url = match['awayTeam']['crest']\n", "\n", "            # Print the match info\n", "\n", "            # Fetch the crests\n", "            home_team_crest = Image.open(BytesIO(requests.get(home_team_crest_url).content))\n", "            away_team_crest = Image.open(BytesIO(requests.get(away_team_crest_url).content))\n", "\n", "            # Print the match info without the image object\n", "            print(f'{formatted_date} - {home_team} vs. {away_team}')\n", "\n", "            # Append the match info to the next_matches list\n", "            COMPETITIONS[competition][\"next_matches\"].append({\n", "                'date': formatted_date,\n", "                'home_team': home_team,\n", "                'away_team': away_team,\n", "                'home_team_crest': home_team_crest,\n", "                'away_team_crest': away_team_crest\n", "            })    \n"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["img = Image.open(BytesIO(requests.get(\"https://crests.football-data.org/90.png\").content))\n", "img"]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\anaconda3\\envs\\aifootball_predictions\\lib\\site-packages\\PIL\\Image.py:1056: UserWarning: Palette images with Transparency expressed in bytes should be converted to RGBA images\n", "  warnings.warn(\n"]}], "source": ["# Save the image as a GIF\n", "img.save(\"output.gif\", format=\"GIF\")"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["E0: Current Matchday 2, Total Matches 380\n", "2024-08-31 11:30:00 - Arsenal FC vs. Brighton & Hove Albion FC\n", "2024-08-31 14:00:00 - Brentford FC vs. Southampton FC\n", "2024-08-31 14:00:00 - Everton FC vs. AFC Bournemouth\n", "2024-08-31 14:00:00 - Ipswich Town FC vs. Fulham FC\n", "2024-08-31 14:00:00 - Leicester City FC vs. Aston Villa FC\n", "2024-08-31 14:00:00 - Nottingham Forest FC vs. Wolverhampton Wanderers FC\n", "2024-08-31 16:30:00 - West Ham United FC vs. Manchester City FC\n", "2024-09-01 12:30:00 - Chelsea FC vs. Crystal Palace FC\n", "2024-09-01 12:30:00 - Newcastle United FC vs. Tottenham Hotspur FC\n", "2024-09-01 15:00:00 - Manchester United FC vs. Liverpool FC\n", "SP1: Current Matchday 3, Total Matches 380\n", "2024-08-31 15:00:00 - FC Barcelona vs. Real Valladolid CF\n", "2024-08-31 17:00:00 - Athletic Club vs. Club Atlético de Madrid\n", "2024-08-31 17:15:00 - RCD Espanyol de Barcelona vs. Rayo Vallecano de Madrid\n", "2024-08-31 19:30:00 - CD Leganés vs. RCD Mallorca\n", "2024-08-31 19:30:00 - Valencia CF vs. Villarreal CF\n", "2024-09-01 15:00:00 - <PERSON> Osasuna vs. RC Celta de Vigo\n", "2024-09-01 15:00:00 - Deportivo Alavés vs. UD Las Palmas\n", "2024-09-01 17:00:00 - Sevilla FC vs. Girona FC\n", "2024-09-01 17:15:00 - Getafe CF vs. Real Sociedad de Fútbol\n", "2024-09-01 19:30:00 - Real Madrid CF vs. Real Betis Balompié\n", "I1: Current Matchday 2, Total Matches 380\n", "2024-08-30 16:30:00 - Venezia FC vs. Torino FC\n", "2024-08-30 18:45:00 - FC Internazionale Milano vs. Atalanta BC\n", "2024-08-31 16:30:00 - Bologna FC 1909 vs. Empoli FC\n", "2024-08-31 16:30:00 - US Lecce vs. Caglia<PERSON>\n", "2024-08-31 18:45:00 - SS Lazio vs. AC Milan\n", "2024-08-31 18:45:00 - SSC Napoli vs. Parma Calcio 1913\n", "2024-09-01 16:30:00 - ACF Fiorentina vs. AC Monza\n", "2024-09-01 16:30:00 - Genoa CFC vs. Hellas Verona FC\n", "2024-09-01 18:45:00 - Juventus FC vs. AS Roma\n", "2024-09-01 18:45:00 - Udinese Calcio vs. Como 1907\n", "D1: Current Matchday 1, Total Matches 306\n", "2024-08-30 18:30:00 - 1. FC Union Berlin vs. FC St. Pauli 1910\n", "2024-08-31 13:30:00 - VfB Stuttgart vs. 1. FSV Mainz 05\n", "2024-08-31 13:30:00 - Eintracht Frankfurt vs. TSG 1899 Hoffenheim\n", "2024-08-31 13:30:00 - SV Werder Bremen vs. Borussia Dortmund\n", "2024-08-31 13:30:00 - VfL Bochum 1848 vs. Borussia Mönchengladbach\n", "2024-08-31 13:30:00 - Holstein Kiel vs. VfL Wolfsburg\n", "2024-08-31 16:30:00 - Bayer 04 Leverkusen vs. RB Leipzig\n", "2024-09-01 13:30:00 - 1. FC Heidenheim 1846 vs. FC Augsburg\n", "2024-09-01 15:30:00 - FC Bayern München vs. SC Freiburg\n", "F1: Current Matchday 2, Total Matches 306\n", "2024-08-30 18:45:00 - Olympique Lyonnais vs. RC Strasbourg Alsace\n", "2024-08-31 15:00:00 - Stade Brestois 29 vs. AS Saint-Étienne\n", "2024-08-31 17:00:00 - Montpellier HSC vs. FC Nantes\n", "2024-08-31 19:00:00 - Toulouse FC vs. Olympique de Marseille\n", "2024-09-01 13:00:00 - AS Monaco FC vs. Racing Club de Lens\n", "2024-09-01 15:00:00 - Angers SCO vs. OGC Nice\n", "2024-09-01 15:00:00 - Le Havre AC vs. AJ Auxerre\n", "2024-09-01 15:00:00 - Stade de Reims vs. Stade Rennais FC 1901\n", "2024-09-01 18:45:00 - Lille OSC vs. Paris Saint-Germain FC\n"]}], "source": ["get_next_matches()"]}, {"cell_type": "code", "execution_count": 49, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'E0': {'id': 2021,\n", "  'crest': 'https://crests.football-data.org/PL.png',\n", "  'next_matches': [{'date': '2024-08-31 11:30:00',\n", "    'home_team': 'Arsenal FC',\n", "    'away_team': 'Brighton & Hove Albion FC',\n", "    'home_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>,\n", "    'away_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>},\n", "   {'date': '2024-08-31 14:00:00',\n", "    'home_team': 'Brentford FC',\n", "    'away_team': 'Southampton FC',\n", "    'home_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>,\n", "    'away_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>},\n", "   {'date': '2024-08-31 14:00:00',\n", "    'home_team': 'Everton FC',\n", "    'away_team': 'AFC Bournemouth',\n", "    'home_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>,\n", "    'away_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>},\n", "   {'date': '2024-08-31 14:00:00',\n", "    'home_team': 'Ipswich Town FC',\n", "    'away_team': 'Fulham FC',\n", "    'home_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=RGBA size=70x70>,\n", "    'away_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>},\n", "   {'date': '2024-08-31 14:00:00',\n", "    'home_team': 'Leicester City FC',\n", "    'away_team': 'Aston Villa FC',\n", "    'home_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>,\n", "    'away_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>},\n", "   {'date': '2024-08-31 14:00:00',\n", "    'home_team': 'Nottingham Forest FC',\n", "    'away_team': 'Wolverhampton Wanderers FC',\n", "    'home_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=RGBA size=70x70>,\n", "    'away_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>},\n", "   {'date': '2024-08-31 16:30:00',\n", "    'home_team': 'West Ham United FC',\n", "    'away_team': 'Manchester City FC',\n", "    'home_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>,\n", "    'away_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>},\n", "   {'date': '2024-09-01 12:30:00',\n", "    'home_team': 'Chelsea FC',\n", "    'away_team': 'Crystal Palace FC',\n", "    'home_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>,\n", "    'away_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>},\n", "   {'date': '2024-09-01 12:30:00',\n", "    'home_team': 'Newcastle United FC',\n", "    'away_team': 'Tottenham Hotspur FC',\n", "    'home_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>,\n", "    'away_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>},\n", "   {'date': '2024-09-01 15:00:00',\n", "    'home_team': 'Manchester United FC',\n", "    'away_team': 'Liverpool FC',\n", "    'home_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>,\n", "    'away_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>}]},\n", " 'SP1': {'id': 2014,\n", "  'crest': 'https://crests.football-data.org/PD.png',\n", "  'next_matches': [{'date': '2024-08-31 15:00:00',\n", "    'home_team': 'FC Barcelona',\n", "    'away_team': 'Real Valladolid CF',\n", "    'home_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>,\n", "    'away_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>},\n", "   {'date': '2024-08-31 17:00:00',\n", "    'home_team': 'Athletic Club',\n", "    'away_team': 'Club Atlético de Madrid',\n", "    'home_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>,\n", "    'away_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>},\n", "   {'date': '2024-08-31 17:15:00',\n", "    'home_team': 'RCD Espanyol de Barcelona',\n", "    'away_team': 'Rayo Vallecano de Madrid',\n", "    'home_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>,\n", "    'away_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>},\n", "   {'date': '2024-08-31 19:30:00',\n", "    'home_team': 'CD Leganés',\n", "    'away_team': 'RCD Mallorca',\n", "    'home_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>,\n", "    'away_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>},\n", "   {'date': '2024-08-31 19:30:00',\n", "    'home_team': 'Valencia CF',\n", "    'away_team': 'Villarreal CF',\n", "    'home_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>,\n", "    'away_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>},\n", "   {'date': '2024-09-01 15:00:00',\n", "    'home_team': '<PERSON> Osasuna',\n", "    'away_team': 'RC Celta de Vigo',\n", "    'home_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>,\n", "    'away_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>},\n", "   {'date': '2024-09-01 15:00:00',\n", "    'home_team': 'Deportivo Alavés',\n", "    'away_team': 'UD Las Palmas',\n", "    'home_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>,\n", "    'away_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>},\n", "   {'date': '2024-09-01 17:00:00',\n", "    'home_team': 'Sevilla FC',\n", "    'away_team': 'Girona FC',\n", "    'home_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>,\n", "    'away_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>},\n", "   {'date': '2024-09-01 17:15:00',\n", "    'home_team': 'Getafe CF',\n", "    'away_team': 'Real Sociedad de Fútbol',\n", "    'home_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>,\n", "    'away_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>},\n", "   {'date': '2024-09-01 19:30:00',\n", "    'home_team': 'Real Madrid CF',\n", "    'away_team': 'Real Betis Balompié',\n", "    'home_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>,\n", "    'away_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>}]},\n", " 'I1': {'id': 2019,\n", "  'crest': 'https://crests.football-data.org/SA.png',\n", "  'next_matches': [{'date': '2024-08-30 16:30:00',\n", "    'home_team': 'Venezia FC',\n", "    'away_team': 'Torino FC',\n", "    'home_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>,\n", "    'away_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>},\n", "   {'date': '2024-08-30 18:45:00',\n", "    'home_team': 'FC Internazionale Milano',\n", "    'away_team': 'Atalanta BC',\n", "    'home_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>,\n", "    'away_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>},\n", "   {'date': '2024-08-31 16:30:00',\n", "    'home_team': 'Bologna FC 1909',\n", "    'away_team': 'Empoli FC',\n", "    'home_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>,\n", "    'away_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>},\n", "   {'date': '2024-08-31 16:30:00',\n", "    'home_team': 'US Lecce',\n", "    'away_team': 'Cagliari Calcio',\n", "    'home_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>,\n", "    'away_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>},\n", "   {'date': '2024-08-31 18:45:00',\n", "    'home_team': 'SS Lazio',\n", "    'away_team': 'AC Milan',\n", "    'home_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>,\n", "    'away_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>},\n", "   {'date': '2024-08-31 18:45:00',\n", "    'home_team': 'SSC Napoli',\n", "    'away_team': 'Parma Calcio 1913',\n", "    'home_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>,\n", "    'away_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>},\n", "   {'date': '2024-09-01 16:30:00',\n", "    'home_team': 'ACF Fiorentina',\n", "    'away_team': 'AC Monza',\n", "    'home_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>,\n", "    'away_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>},\n", "   {'date': '2024-09-01 16:30:00',\n", "    'home_team': 'Genoa CFC',\n", "    'away_team': 'Hellas Verona FC',\n", "    'home_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>,\n", "    'away_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>},\n", "   {'date': '2024-09-01 18:45:00',\n", "    'home_team': 'Juventus FC',\n", "    'away_team': 'AS Roma',\n", "    'home_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>,\n", "    'away_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>},\n", "   {'date': '2024-09-01 18:45:00',\n", "    'home_team': 'Udinese Calcio',\n", "    'away_team': 'Como 1907',\n", "    'home_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>,\n", "    'away_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>}]},\n", " 'D1': {'id': 2002,\n", "  'crest': 'https://crests.football-data.org/BL1.png',\n", "  'next_matches': [{'date': '2024-08-30 18:30:00',\n", "    'home_team': '1. FC Union Berlin',\n", "    'away_team': 'FC St. Pauli 1910',\n", "    'home_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>,\n", "    'away_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>},\n", "   {'date': '2024-08-31 13:30:00',\n", "    'home_team': 'VfB Stuttgart',\n", "    'away_team': '1. FSV Mainz 05',\n", "    'home_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=RGBA size=70x70>,\n", "    'away_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=RGBA size=70x70>},\n", "   {'date': '2024-08-31 13:30:00',\n", "    'home_team': 'Eintracht Frankfurt',\n", "    'away_team': 'TSG 1899 Hoffenheim',\n", "    'home_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>,\n", "    'away_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>},\n", "   {'date': '2024-08-31 13:30:00',\n", "    'home_team': 'SV Werder Bremen',\n", "    'away_team': 'Borussia Dortmund',\n", "    'home_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>,\n", "    'away_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>},\n", "   {'date': '2024-08-31 13:30:00',\n", "    'home_team': 'VfL Bochum 1848',\n", "    'away_team': 'Borussia Mönchengladbach',\n", "    'home_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>,\n", "    'away_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=RGBA size=70x70>},\n", "   {'date': '2024-08-31 13:30:00',\n", "    'home_team': 'Holstein Kiel',\n", "    'away_team': 'VfL Wolfsburg',\n", "    'home_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>,\n", "    'away_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>},\n", "   {'date': '2024-08-31 16:30:00',\n", "    'home_team': 'Bayer 04 Leverkusen',\n", "    'away_team': 'RB Leipzig',\n", "    'home_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>,\n", "    'away_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>},\n", "   {'date': '2024-09-01 13:30:00',\n", "    'home_team': '1. FC Heidenheim 1846',\n", "    'away_team': 'FC Augsburg',\n", "    'home_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>,\n", "    'away_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=RGBA size=70x70>},\n", "   {'date': '2024-09-01 15:30:00',\n", "    'home_team': 'FC Bayern München',\n", "    'away_team': 'SC Freiburg',\n", "    'home_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>,\n", "    'away_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>}]},\n", " 'F1': {'id': 2015,\n", "  'crest': 'https://crests.football-data.org/FL1.png',\n", "  'next_matches': [{'date': '2024-08-30 18:45:00',\n", "    'home_team': 'Olympique Lyonnais',\n", "    'away_team': 'RC Strasbourg Alsace',\n", "    'home_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>,\n", "    'away_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>},\n", "   {'date': '2024-08-31 15:00:00',\n", "    'home_team': 'Stade Brestois 29',\n", "    'away_team': 'AS Saint-Étienne',\n", "    'home_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>,\n", "    'away_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>},\n", "   {'date': '2024-08-31 17:00:00',\n", "    'home_team': 'Montpellier HSC',\n", "    'away_team': 'FC Nantes',\n", "    'home_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>,\n", "    'away_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>},\n", "   {'date': '2024-08-31 19:00:00',\n", "    'home_team': 'Toulouse FC',\n", "    'away_team': 'Olympique de Marseille',\n", "    'home_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>,\n", "    'away_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>},\n", "   {'date': '2024-09-01 13:00:00',\n", "    'home_team': 'AS Monaco FC',\n", "    'away_team': 'Racing Club de Lens',\n", "    'home_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=RGBA size=70x70>,\n", "    'away_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>},\n", "   {'date': '2024-09-01 15:00:00',\n", "    'home_team': 'Angers SCO',\n", "    'away_team': 'OGC Nice',\n", "    'home_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>,\n", "    'away_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>},\n", "   {'date': '2024-09-01 15:00:00',\n", "    'home_team': 'Le Havre AC',\n", "    'away_team': '<PERSON>',\n", "    'home_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>,\n", "    'away_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=RGBA size=70x70>},\n", "   {'date': '2024-09-01 15:00:00',\n", "    'home_team': 'Stade de Reims',\n", "    'away_team': 'Stade Rennais FC 1901',\n", "    'home_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>,\n", "    'away_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>},\n", "   {'date': '2024-09-01 18:45:00',\n", "    'home_team': 'Lille OSC',\n", "    'away_team': 'Paris Saint-Germain FC',\n", "    'home_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>,\n", "    'away_team_crest': <PIL.PngImagePlugin.PngImageFile image mode=P size=200x200>}]}}"]}, "execution_count": 49, "metadata": {}, "output_type": "execute_result"}], "source": ["COMPETITIONS"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"data": {"text/plain": ["['Leverkusen',\n", " 'Hoffenheim',\n", " 'Werder Bremen',\n", " 'Schalke 04',\n", " 'FC Koln',\n", " 'Stuttgart',\n", " 'Union Berlin',\n", " 'Dortmund',\n", " 'Freiburg',\n", " 'Mainz',\n", " '<PERSON><PERSON>',\n", " 'Bayern Munich',\n", " 'Wolfsburg',\n", " 'RB Leipzig',\n", " 'Ein Frankfurt',\n", " 'Bochum',\n", " \"<PERSON><PERSON><PERSON><PERSON>\",\n", " 'Heidenheim',\n", " 'Darmstadt',\n", " 'Augsburg',\n", " 'Crystal Palace',\n", " 'Bournemouth',\n", " 'Man United',\n", " 'Brentford',\n", " 'Leeds',\n", " 'Southampton',\n", " 'Chelsea',\n", " 'Wolves',\n", " 'Brighton',\n", " 'Tottenham',\n", " 'Everton',\n", " 'Aston Villa',\n", " 'Leicester',\n", " 'Fulham',\n", " 'Liverpool',\n", " 'West Ham',\n", " 'Man City',\n", " 'Newcastle',\n", " \"Nott'm Forest\",\n", " 'Luton',\n", " 'Burnley',\n", " 'Sheffield United',\n", " 'Arsenal',\n", " 'Ipswich',\n", " 'Lyon',\n", " 'Rennes',\n", " 'Montpellier',\n", " 'Brest',\n", " 'Marseille',\n", " 'Troyes',\n", " 'Auxerre',\n", " 'Nantes',\n", " 'Toulouse',\n", " 'Monaco',\n", " 'Angers',\n", " 'Nice',\n", " '<PERSON><PERSON>',\n", " 'Reims',\n", " 'Clermont',\n", " 'Strasbourg',\n", " 'Lille',\n", " 'Paris SG',\n", " 'Lens',\n", " '<PERSON><PERSON><PERSON><PERSON>',\n", " 'Le Havre',\n", " 'Metz',\n", " 'Sampdor<PERSON>',\n", " 'Verona',\n", " 'Monza',\n", " 'Roma',\n", " 'Udinese',\n", " 'Empoli',\n", " 'Lecce',\n", " 'Spezia',\n", " 'Bologna',\n", " 'Juventus',\n", " 'Sassuolo',\n", " 'Lazio',\n", " 'Milan',\n", " 'Napoli',\n", " 'Cremonese',\n", " 'Fiorentina',\n", " 'Torino',\n", " 'Salernitana',\n", " 'Inter',\n", " 'Frosinone',\n", " 'Genoa',\n", " 'Cagliari',\n", " 'Atalanta',\n", " 'Parma',\n", " 'Sampdor<PERSON>',\n", " 'Verona',\n", " 'Monza',\n", " 'Roma',\n", " 'Udinese',\n", " 'Empoli',\n", " 'Lecce',\n", " 'Spezia',\n", " 'Bologna',\n", " 'Juventus',\n", " 'Sassuolo',\n", " 'Lazio',\n", " 'Milan',\n", " 'Napoli',\n", " 'Cremonese',\n", " 'Fiorentina',\n", " 'Torino',\n", " 'Salernitana',\n", " 'Inter',\n", " 'Frosinone',\n", " 'Genoa',\n", " 'Cagliari',\n", " 'Atalanta',\n", " 'Parma',\n", " 'Cadiz',\n", " 'Getafe',\n", " 'Vallecano',\n", " 'Celta',\n", " '<PERSON><PERSON><PERSON>',\n", " 'Ath Madrid',\n", " 'Barcelona',\n", " 'Mallorca',\n", " 'Girona',\n", " 'Sociedad',\n", " 'Sevilla',\n", " 'Almeria',\n", " 'Betis',\n", " '<PERSON><PERSON><PERSON><PERSON>',\n", " 'Ath Bilbao',\n", " 'Granada',\n", " 'Valencia',\n", " 'Real Madrid',\n", " 'Las Palmas',\n", " 'Elche',\n", " '<PERSON><PERSON><PERSON><PERSON>',\n", " 'Espanol',\n", " 'Alaves']"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["# read all the csv files from the data/processed folder\n", "# walk through the data/raw folder and read all the csv files\n", "\n", "directory_path = '../data/processed'  # Replace with your directory path\n", "dataframes = {}\n", "\n", "# Define a list which contains all the unique teams names\n", "column_name = \"HomeTeam\"\n", "full_teams_names = []\n", "# Walk through the directory and find CSV files\n", "for root, dirs, files in os.walk(directory_path):\n", "    for file in files:\n", "        if file.endswith('.csv'):\n", "            file_path = os.path.join(root, file)\n", "            df = pd.read_csv(file_path)\n", "\n", "            # Get the unique team names\n", "            teams_name = (df[column_name].unique()).tolist()\n", "            \n", "        full_teams_names.extend(teams_name)\n", "\n", "full_teams_names"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### test with fuzzy"]}, {"cell_type": "code", "execution_count": 127, "metadata": {}, "outputs": [], "source": ["# Function to find the best match from full_teams_names for a given team name\n", "def get_best_match(team_name, team_names_list, threshold=50):\n", "    best_match = process.extractOne(team_name, team_names_list)\n", "    if best_match and best_match[1] >= threshold:  # Match must be above the threshold\n", "        return best_match[0]\n", "    else:\n", "        return None  # No suitable match found\n", "\n", "# Function to replace team names in the next_matches dictionary\n", "def replace_team_names(matches_dict, team_names_list):\n", "    unmatched_teams_in_dict = set()\n", "    matched_teams = set()\n", "\n", "    for league, matches in matches_dict.items():\n", "        for match in matches:\n", "            home_team = get_best_match(match['home_team'], team_names_list)\n", "            away_team = get_best_match(match['away_team'], team_names_list)\n", "\n", "            if home_team:\n", "                match['home_team'] = home_team\n", "                matched_teams.add(home_team)\n", "            else:\n", "                unmatched_teams_in_dict.add(match['home_team'])\n", "\n", "            if away_team:\n", "                match['away_team'] = away_team\n", "                matched_teams.add(away_team)\n", "            else:\n", "                unmatched_teams_in_dict.add(match['away_team'])\n", "\n", "    unmatched_teams_in_list = set(team_names_list) - matched_teams\n", "\n", "    return matches_dict, unmatched_teams_in_dict, unmatched_teams_in_list"]}, {"cell_type": "code", "execution_count": 128, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Updated Matches:\n", "{'E0': [{'date': '2024-08-31 11:30:00', 'home_team': 'Arsenal', 'away_team': 'Brighton'}, {'date': '2024-08-31 14:00:00', 'home_team': 'Brentford', 'away_team': 'Southampton'}, {'date': '2024-08-31 14:00:00', 'home_team': 'Everton', 'away_team': 'Bournemouth'}, {'date': '2024-08-31 14:00:00', 'home_team': 'Ipswich', 'away_team': 'Fulham'}, {'date': '2024-08-31 14:00:00', 'home_team': 'Leicester', 'away_team': 'Aston Villa'}, {'date': '2024-08-31 14:00:00', 'home_team': 'FC Koln', 'away_team': 'FC Koln'}, {'date': '2024-08-31 16:30:00', 'home_team': 'West Ham', 'away_team': 'FC Koln'}, {'date': '2024-09-01 12:30:00', 'home_team': 'Chelsea', 'away_team': 'Crystal Palace'}, {'date': '2024-09-01 12:30:00', 'home_team': 'Newcastle', 'away_team': 'Tottenham'}, {'date': '2024-09-01 15:00:00', 'home_team': 'FC Koln', 'away_team': 'Liverpool'}], 'SP1': [{'date': '2024-08-26 19:30:00', 'home_team': 'Villarreal', 'away_team': 'Celta'}, {'date': '2024-08-27 17:00:00', 'home_team': 'Mallorca', 'away_team': 'Sevilla'}, {'date': '2024-08-27 19:30:00', 'home_team': 'Vallecano', 'away_team': 'Barcelona'}, {'date': '2024-08-28 00:00:00', 'home_team': 'Betis', 'away_team': 'Getafe'}, {'date': '2024-08-28 17:00:00', 'home_team': 'Lecce', 'away_team': 'Valencia'}, {'date': '2024-08-28 17:00:00', 'home_team': 'Valladolid', 'away_team': 'Lens'}, {'date': '2024-08-28 19:30:00', 'home_team': 'Sociedad', 'away_team': 'Alaves'}, {'date': '2024-08-28 19:30:00', 'home_team': 'Ath Madrid', 'away_team': 'Barcelona'}, {'date': '2024-08-29 17:00:00', 'home_team': 'Girona', 'away_team': 'Osasuna'}, {'date': '2024-08-29 19:30:00', 'home_team': 'Las Palmas', 'away_team': 'Real Madrid'}], 'I1': [{'date': '2024-08-30 16:30:00', 'home_team': 'Spezia', 'away_team': 'Torino'}, {'date': '2024-08-30 18:45:00', 'home_team': 'Milan', 'away_team': 'Atalanta'}, {'date': '2024-08-31 16:30:00', 'home_team': 'Bologna', 'away_team': 'Empoli'}, {'date': '2024-08-31 16:30:00', 'home_team': 'Lecce', 'away_team': 'Cagliari'}, {'date': '2024-08-31 18:45:00', 'home_team': 'Lazio', 'away_team': 'Milan'}, {'date': '2024-08-31 18:45:00', 'home_team': 'Napoli', 'away_team': 'Parma'}, {'date': '2024-09-01 16:30:00', 'home_team': 'Fiorentina', 'away_team': 'Monza'}, {'date': '2024-09-01 16:30:00', 'home_team': 'Genoa', 'away_team': 'Verona'}, {'date': '2024-09-01 18:45:00', 'home_team': 'Juventus', 'away_team': 'Roma'}, {'date': '2024-09-01 18:45:00', 'home_team': 'Udinese', 'away_team': 'Como 1907'}], 'D1': [{'date': '2024-08-30 18:30:00', 'home_team': 'Union Berlin', 'away_team': 'FC Koln'}, {'date': '2024-08-31 13:30:00', 'home_team': 'Stuttgart', 'away_team': 'Mainz'}, {'date': '2024-08-31 13:30:00', 'home_team': 'Ein Frankfurt', 'away_team': 'Hoffenheim'}, {'date': '2024-08-31 13:30:00', 'home_team': 'Werder Bremen', 'away_team': 'Dortmund'}, {'date': '2024-08-31 13:30:00', 'home_team': 'Bochum', 'away_team': \"M'gladbach\"}, {'date': '2024-08-31 13:30:00', 'home_team': 'Chelsea', 'away_team': 'Wolfsburg'}, {'date': '2024-08-31 16:30:00', 'home_team': 'Leverkusen', 'away_team': 'RB Leipzig'}, {'date': '2024-09-01 13:30:00', 'home_team': 'Heidenheim', 'away_team': 'Augsburg'}, {'date': '2024-09-01 15:30:00', 'home_team': 'FC Koln', 'away_team': 'Freiburg'}], 'F1': [{'date': '2024-08-30 18:45:00', 'home_team': 'Lyon', 'away_team': 'Strasbourg'}, {'date': '2024-08-31 15:00:00', 'home_team': 'Brest', 'away_team': 'Rennes'}, {'date': '2024-08-31 17:00:00', 'home_team': 'Montpellier', 'away_team': 'Nantes'}, {'date': '2024-08-31 19:00:00', 'home_team': 'Toulouse', 'away_team': 'Marseille'}, {'date': '2024-09-01 13:00:00', 'home_team': 'Monaco', 'away_team': 'Lens'}, {'date': '2024-09-01 15:00:00', 'home_team': 'Angers', 'away_team': 'Nice'}, {'date': '2024-09-01 15:00:00', 'home_team': 'Le Havre', 'away_team': 'Auxerre'}, {'date': '2024-09-01 15:00:00', 'home_team': 'Reims', 'away_team': 'FC Koln'}, {'date': '2024-09-01 18:45:00', 'home_team': 'Lille', 'away_team': 'FC Koln'}]}\n", "\n", "Unmatched Teams in Dictionary:\n", "{'Como 1907'}\n", "\n", "Unmatched Teams in List:\n", "{'<PERSON><PERSON>', 'Man City', 'Salernitana', 'Leeds', 'Cadiz', 'Burnley', 'Darmstadt', 'Clermont', 'Wolves', 'Espanol', 'Ajaccio', 'Elche', 'Cremonese', 'Granada', 'Bayern Munich', 'Inter', \"Nott'm Forest\", 'Frosinone', 'Lorient', 'Luton', 'Metz', 'Schalke 04', 'Troyes', 'Sampdoria', 'Man United', 'Sassuolo', 'Ath Bilbao', 'Paris SG', 'Almeria', 'Sheffield United'}\n"]}], "source": ["# Apply the function to update the next_matches dictionary\n", "updated_matches, unmatched_in_dict, unmatched_in_list = replace_team_names(next_matches, full_teams_names)\n", "\n", "# Output the results\n", "print(\"Updated Matches:\")\n", "print(updated_matches)\n", "\n", "print(\"\\nUnmatched Teams in Dictionary:\")\n", "print(unmatched_in_dict)\n", "\n", "print(\"\\nUnmatched Teams in List:\")\n", "print(unmatched_in_list)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Manual Mapping"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [], "source": ["team_name_mapping = {\n", "    'Arsenal FC': 'Arsenal',\n", "    'Brighton & Hove Albion FC': 'Brighton',\n", "    'Brentford FC': 'Brentford',\n", "    'Southampton FC': 'Southampton',\n", "    'Everton FC': 'Everton',\n", "    'AFC Bournemouth': 'Bournemouth',\n", "    'Ipswich Town FC': 'Ipswich',\n", "    'Fulham FC': 'Fulham',\n", "    'Leicester City FC': 'Leicester',\n", "    'Aston Villa FC': 'Aston Villa',\n", "    'Nottingham Forest FC': \"Nott'm Forest\",\n", "    'Wolverhampton Wanderers FC': 'Wolves',\n", "    'West Ham United FC': 'West Ham',\n", "    'Manchester City FC': 'Man City',\n", "    'Chelsea FC': 'Chelsea',\n", "    'Crystal Palace FC': 'Crystal Palace',\n", "    'Newcastle United FC': 'Newcastle',\n", "    'Tottenham Hotspur FC': 'Tottenham',\n", "    'Manchester United FC': 'Man United',\n", "    'Liverpool FC': 'Liverpool',\n", "    'Villarreal CF': 'Villarreal',\n", "    'RC Celta de Vigo': 'Celta',\n", "    'RCD Mallorca': 'Mallorca',\n", "    'Sevilla FC': 'Sevilla',\n", "    'Rayo Vallecano de Madrid': 'Vallecano',\n", "    'FC Barcelona': 'Barcelona',\n", "    'Real Betis Balompié': 'Betis',\n", "    'Getafe CF': 'Getafe',\n", "    'Athletic Club': 'Ath Bilbao',\n", "    'Valencia CF': 'Valencia',\n", "    'Real Valladolid CF': 'Valladolid',\n", "    'CD Leganés': 'Leganes',\n", "    'Real Sociedad de Fútbol': 'Sociedad',\n", "    'Deportivo Alavés': 'Alaves',\n", "    'Club Atlético de Madrid': 'Ath Madrid',\n", "    'RCD Espanyol de Barcelona': 'Espanol',\n", "    'Girona FC': 'Girona',\n", "    '<PERSON> Osasuna': '<PERSON><PERSON><PERSON><PERSON>',\n", "    'UD Las Palmas': 'Las Palmas',\n", "    'Real Madrid CF': 'Real Madrid',\n", "    'Venezia FC': 'Venezia',\n", "    'Torino FC': 'Torino',\n", "    'FC Internazionale Milano': 'Inter',\n", "    'Atalanta BC': 'Atalanta',\n", "    'Bologna FC 1909': 'Bologna',\n", "    'Empoli FC': 'Empoli',\n", "    'US Lecce': 'Lecce',\n", "    'Cagliari Calcio': 'Cagliari',\n", "    'SS Lazio': 'Lazio',\n", "    'AC Milan': 'Milan',\n", "    'SSC Napoli': 'Napoli',\n", "    'Parma Calcio 1913': 'Parma',\n", "    'ACF Fiorentina': 'Fiorentina',\n", "    'AC Monza': 'Mon<PERSON>',\n", "    'Genoa CFC': 'Genoa',\n", "    'Hellas Verona FC': 'Verona',\n", "    'Juventus FC': 'Juventus',\n", "    'AS Roma': 'Roma',\n", "    'Udinese Calcio': 'Udinese',\n", "    'Como 1907': 'Como', \n", "    '1. FC Union Berlin': 'Union Berlin',\n", "    'FC St. Pauli 1910': None,  # No match found in the list\n", "    'VfB Stuttgart': 'Stuttgart',\n", "    '1. FSV Mainz 05': 'Mainz',\n", "    'Eintracht Frankfurt': 'Ein Frankfurt',\n", "    'TSG 1899 Hoffenheim': 'Hoffenheim',\n", "    'SV Werder Bremen': 'Werder Bremen',\n", "    'Borussia Dortmund': 'Dortmund',\n", "    'VfL Bochum 1848': 'Bochum',\n", "    'Borussia Mönchengladbach': \"M'<PERSON><PERSON>\",\n", "    'Holstein Kiel': None,  # No match found in the list\n", "    'VfL Wolfsburg': 'Wolfsburg',\n", "    'Bayer 04 Leverkusen': 'Leverkusen',\n", "    'RB Leipzig': 'RB Leipzig',\n", "    '1. FC Heidenheim 1846': 'Heidenheim',\n", "    'FC Augsburg': 'Augsburg',\n", "    'FC Bayern München': 'Bayern Munich',\n", "    'SC Freiburg': 'Freiburg',\n", "    'Olympique Lyonnais': 'Lyon',\n", "    'RC Strasbourg Alsace': 'Strasbourg',\n", "    'Stade Brestois 29': 'Brest',\n", "    'AS Saint-Étienne': 'St Etienne',\n", "    'Montpellier HSC': 'Montpellier',\n", "    'FC Nantes': 'Nantes',\n", "    'Toulouse FC': 'Toulouse',\n", "    'Olympique de Marseille': 'Marseille',\n", "    'AS Monaco FC': 'Monaco',\n", "    'Angers SCO': 'Angers',\n", "    'OGC Nice': 'Nice',\n", "    'Le Havre AC': 'Le Havre',\n", "    '<PERSON>': '<PERSON><PERSON><PERSON>',\n", "    'Stade de Reims': 'Reims',\n", "    'Stade Rennais FC 1901': 'Rennes',\n", "    'Lille OSC': 'Lille',\n", "    'Paris Saint-Germain FC': 'Paris SG',\n", "    'Racing Club de Lens': 'Lens',\n", "    'AC Ajaccio': '<PERSON><PERSON><PERSON><PERSON>',\n", "    'FC Metz': 'Metz',\n", "}"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [], "source": ["# Function to replace team names in the dictionary using the mapping\n", "def replace_team_names(matches_dict, name_mapping):\n", "    for league, leagues_info in matches_dict.items():\n", "        for match in leagues_info[\"next_matches\"]:\n", "            if match['home_team'] in name_mapping:\n", "                match['home_team'] = name_mapping[match['home_team']]\n", "            if match['away_team'] in name_mapping:\n", "                match['away_team'] = name_mapping[match['away_team']]\n", "    return matches_dict"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'E0': {'id': 2021,\n", "  'crest': 'https://crests.football-data.org/PL.png',\n", "  'next_matches': [{'date': '2024-08-31 11:30:00',\n", "    'home_team': 'Arsenal',\n", "    'away_team': 'Brighton',\n", "    'home_team_crest': 'https://crests.football-data.org/57.png',\n", "    'away_team_crest': 'https://crests.football-data.org/397.png'},\n", "   {'date': '2024-08-31 14:00:00',\n", "    'home_team': 'Brentford',\n", "    'away_team': 'Southampton',\n", "    'home_team_crest': 'https://crests.football-data.org/402.png',\n", "    'away_team_crest': 'https://crests.football-data.org/340.png'},\n", "   {'date': '2024-08-31 14:00:00',\n", "    'home_team': 'Everton',\n", "    'away_team': 'Bournemouth',\n", "    'home_team_crest': 'https://crests.football-data.org/62.png',\n", "    'away_team_crest': 'https://crests.football-data.org/1044.png'},\n", "   {'date': '2024-08-31 14:00:00',\n", "    'home_team': 'Ipswich',\n", "    'away_team': 'Fulham',\n", "    'home_team_crest': 'https://crests.football-data.org/349.png',\n", "    'away_team_crest': 'https://crests.football-data.org/63.png'},\n", "   {'date': '2024-08-31 14:00:00',\n", "    'home_team': 'Leicester',\n", "    'away_team': 'Aston Villa',\n", "    'home_team_crest': 'https://crests.football-data.org/338.png',\n", "    'away_team_crest': 'https://crests.football-data.org/58.png'},\n", "   {'date': '2024-08-31 14:00:00',\n", "    'home_team': \"Nott'm Forest\",\n", "    'away_team': 'Wolves',\n", "    'home_team_crest': 'https://crests.football-data.org/351.png',\n", "    'away_team_crest': 'https://crests.football-data.org/76.png'},\n", "   {'date': '2024-08-31 16:30:00',\n", "    'home_team': 'West Ham',\n", "    'away_team': 'Man City',\n", "    'home_team_crest': 'https://crests.football-data.org/563.png',\n", "    'away_team_crest': 'https://crests.football-data.org/65.png'},\n", "   {'date': '2024-09-01 12:30:00',\n", "    'home_team': '<PERSON>',\n", "    'away_team': 'Crystal Palace',\n", "    'home_team_crest': 'https://crests.football-data.org/61.png',\n", "    'away_team_crest': 'https://crests.football-data.org/354.png'},\n", "   {'date': '2024-09-01 12:30:00',\n", "    'home_team': 'Newcastle',\n", "    'away_team': 'Tottenham',\n", "    'home_team_crest': 'https://crests.football-data.org/67.png',\n", "    'away_team_crest': 'https://crests.football-data.org/73.png'},\n", "   {'date': '2024-09-01 15:00:00',\n", "    'home_team': 'Man United',\n", "    'away_team': 'Liverpool',\n", "    'home_team_crest': 'https://crests.football-data.org/66.png',\n", "    'away_team_crest': 'https://crests.football-data.org/64.png'},\n", "   {'date': '2024-08-31 11:30:00',\n", "    'home_team': 'Arsenal',\n", "    'away_team': 'Brighton',\n", "    'home_team_crest': 'https://crests.football-data.org/57.png',\n", "    'away_team_crest': 'https://crests.football-data.org/397.png'},\n", "   {'date': '2024-08-31 14:00:00',\n", "    'home_team': 'Brentford',\n", "    'away_team': 'Southampton',\n", "    'home_team_crest': 'https://crests.football-data.org/402.png',\n", "    'away_team_crest': 'https://crests.football-data.org/340.png'},\n", "   {'date': '2024-08-31 14:00:00',\n", "    'home_team': 'Everton',\n", "    'away_team': 'Bournemouth',\n", "    'home_team_crest': 'https://crests.football-data.org/62.png',\n", "    'away_team_crest': 'https://crests.football-data.org/1044.png'},\n", "   {'date': '2024-08-31 14:00:00',\n", "    'home_team': 'Ipswich',\n", "    'away_team': 'Fulham',\n", "    'home_team_crest': 'https://crests.football-data.org/349.png',\n", "    'away_team_crest': 'https://crests.football-data.org/63.png'},\n", "   {'date': '2024-08-31 14:00:00',\n", "    'home_team': 'Leicester',\n", "    'away_team': 'Aston Villa',\n", "    'home_team_crest': 'https://crests.football-data.org/338.png',\n", "    'away_team_crest': 'https://crests.football-data.org/58.png'},\n", "   {'date': '2024-08-31 14:00:00',\n", "    'home_team': \"Nott'm Forest\",\n", "    'away_team': 'Wolves',\n", "    'home_team_crest': 'https://crests.football-data.org/351.png',\n", "    'away_team_crest': 'https://crests.football-data.org/76.png'},\n", "   {'date': '2024-08-31 16:30:00',\n", "    'home_team': 'West Ham',\n", "    'away_team': 'Man City',\n", "    'home_team_crest': 'https://crests.football-data.org/563.png',\n", "    'away_team_crest': 'https://crests.football-data.org/65.png'},\n", "   {'date': '2024-09-01 12:30:00',\n", "    'home_team': '<PERSON>',\n", "    'away_team': 'Crystal Palace',\n", "    'home_team_crest': 'https://crests.football-data.org/61.png',\n", "    'away_team_crest': 'https://crests.football-data.org/354.png'},\n", "   {'date': '2024-09-01 12:30:00',\n", "    'home_team': 'Newcastle',\n", "    'away_team': 'Tottenham',\n", "    'home_team_crest': 'https://crests.football-data.org/67.png',\n", "    'away_team_crest': 'https://crests.football-data.org/73.png'},\n", "   {'date': '2024-09-01 15:00:00',\n", "    'home_team': 'Man United',\n", "    'away_team': 'Liverpool',\n", "    'home_team_crest': 'https://crests.football-data.org/66.png',\n", "    'away_team_crest': 'https://crests.football-data.org/64.png'}]},\n", " 'SP1': {'id': 2014,\n", "  'crest': 'https://crests.football-data.org/PD.png',\n", "  'next_matches': [{'date': '2024-08-31 15:00:00',\n", "    'home_team': 'Barcelona',\n", "    'away_team': '<PERSON><PERSON><PERSON><PERSON>',\n", "    'home_team_crest': 'https://crests.football-data.org/81.png',\n", "    'away_team_crest': 'https://crests.football-data.org/250.png'},\n", "   {'date': '2024-08-31 17:00:00',\n", "    'home_team': 'Ath Bilbao',\n", "    'away_team': 'Ath Madrid',\n", "    'home_team_crest': 'https://crests.football-data.org/77.png',\n", "    'away_team_crest': 'https://crests.football-data.org/78.png'},\n", "   {'date': '2024-08-31 17:15:00',\n", "    'home_team': 'Espanol',\n", "    'away_team': 'Vallecano',\n", "    'home_team_crest': 'https://crests.football-data.org/80.png',\n", "    'away_team_crest': 'https://crests.football-data.org/87.png'},\n", "   {'date': '2024-08-31 19:30:00',\n", "    'home_team': 'Legan<PERSON>',\n", "    'away_team': 'Mallorca',\n", "    'home_team_crest': 'https://crests.football-data.org/745.png',\n", "    'away_team_crest': 'https://crests.football-data.org/89.png'},\n", "   {'date': '2024-08-31 19:30:00',\n", "    'home_team': 'Valencia',\n", "    'away_team': '<PERSON><PERSON><PERSON>',\n", "    'home_team_crest': 'https://crests.football-data.org/95.png',\n", "    'away_team_crest': 'https://crests.football-data.org/94.png'},\n", "   {'date': '2024-09-01 15:00:00',\n", "    'home_team': '<PERSON><PERSON><PERSON><PERSON>',\n", "    'away_team': '<PERSON><PERSON>',\n", "    'home_team_crest': 'https://crests.football-data.org/79.png',\n", "    'away_team_crest': 'https://crests.football-data.org/558.png'},\n", "   {'date': '2024-09-01 15:00:00',\n", "    'home_team': '<PERSON><PERSON>',\n", "    'away_team': 'Las Palmas',\n", "    'home_team_crest': 'https://crests.football-data.org/263.png',\n", "    'away_team_crest': 'https://crests.football-data.org/275.png'},\n", "   {'date': '2024-09-01 17:00:00',\n", "    'home_team': 'Sevilla',\n", "    'away_team': 'Girona',\n", "    'home_team_crest': 'https://crests.football-data.org/559.png',\n", "    'away_team_crest': 'https://crests.football-data.org/298.png'},\n", "   {'date': '2024-09-01 17:15:00',\n", "    'home_team': 'Getafe',\n", "    'away_team': 'Sociedad',\n", "    'home_team_crest': 'https://crests.football-data.org/82.png',\n", "    'away_team_crest': 'https://crests.football-data.org/92.png'},\n", "   {'date': '2024-09-01 19:30:00',\n", "    'home_team': 'Real Madrid',\n", "    'away_team': 'Betis',\n", "    'home_team_crest': 'https://crests.football-data.org/86.png',\n", "    'away_team_crest': 'https://crests.football-data.org/90.png'},\n", "   {'date': '2024-08-31 15:00:00',\n", "    'home_team': 'Barcelona',\n", "    'away_team': '<PERSON><PERSON><PERSON><PERSON>',\n", "    'home_team_crest': 'https://crests.football-data.org/81.png',\n", "    'away_team_crest': 'https://crests.football-data.org/250.png'},\n", "   {'date': '2024-08-31 17:00:00',\n", "    'home_team': 'Ath Bilbao',\n", "    'away_team': 'Ath Madrid',\n", "    'home_team_crest': 'https://crests.football-data.org/77.png',\n", "    'away_team_crest': 'https://crests.football-data.org/78.png'},\n", "   {'date': '2024-08-31 17:15:00',\n", "    'home_team': 'Espanol',\n", "    'away_team': 'Vallecano',\n", "    'home_team_crest': 'https://crests.football-data.org/80.png',\n", "    'away_team_crest': 'https://crests.football-data.org/87.png'},\n", "   {'date': '2024-08-31 19:30:00',\n", "    'home_team': 'Legan<PERSON>',\n", "    'away_team': 'Mallorca',\n", "    'home_team_crest': 'https://crests.football-data.org/745.png',\n", "    'away_team_crest': 'https://crests.football-data.org/89.png'},\n", "   {'date': '2024-08-31 19:30:00',\n", "    'home_team': 'Valencia',\n", "    'away_team': '<PERSON><PERSON><PERSON>',\n", "    'home_team_crest': 'https://crests.football-data.org/95.png',\n", "    'away_team_crest': 'https://crests.football-data.org/94.png'},\n", "   {'date': '2024-09-01 15:00:00',\n", "    'home_team': '<PERSON><PERSON><PERSON><PERSON>',\n", "    'away_team': '<PERSON><PERSON>',\n", "    'home_team_crest': 'https://crests.football-data.org/79.png',\n", "    'away_team_crest': 'https://crests.football-data.org/558.png'},\n", "   {'date': '2024-09-01 15:00:00',\n", "    'home_team': '<PERSON><PERSON>',\n", "    'away_team': 'Las Palmas',\n", "    'home_team_crest': 'https://crests.football-data.org/263.png',\n", "    'away_team_crest': 'https://crests.football-data.org/275.png'},\n", "   {'date': '2024-09-01 17:00:00',\n", "    'home_team': 'Sevilla',\n", "    'away_team': 'Girona',\n", "    'home_team_crest': 'https://crests.football-data.org/559.png',\n", "    'away_team_crest': 'https://crests.football-data.org/298.png'},\n", "   {'date': '2024-09-01 17:15:00',\n", "    'home_team': 'Getafe',\n", "    'away_team': 'Sociedad',\n", "    'home_team_crest': 'https://crests.football-data.org/82.png',\n", "    'away_team_crest': 'https://crests.football-data.org/92.png'},\n", "   {'date': '2024-09-01 19:30:00',\n", "    'home_team': 'Real Madrid',\n", "    'away_team': 'Betis',\n", "    'home_team_crest': 'https://crests.football-data.org/86.png',\n", "    'away_team_crest': 'https://crests.football-data.org/90.png'}]},\n", " 'I1': {'id': 2019,\n", "  'crest': 'https://crests.football-data.org/SA.png',\n", "  'next_matches': [{'date': '2024-08-30 16:30:00',\n", "    'home_team': 'Venezia',\n", "    'away_team': 'Torino',\n", "    'home_team_crest': 'https://crests.football-data.org/454.png',\n", "    'away_team_crest': 'https://crests.football-data.org/586.png'},\n", "   {'date': '2024-08-30 18:45:00',\n", "    'home_team': 'Inter',\n", "    'away_team': 'Atalanta',\n", "    'home_team_crest': 'https://crests.football-data.org/108.png',\n", "    'away_team_crest': 'https://crests.football-data.org/102.png'},\n", "   {'date': '2024-08-31 16:30:00',\n", "    'home_team': 'Bologna',\n", "    'away_team': '<PERSON><PERSON>',\n", "    'home_team_crest': 'https://crests.football-data.org/103.png',\n", "    'away_team_crest': 'https://crests.football-data.org/445.png'},\n", "   {'date': '2024-08-31 16:30:00',\n", "    'home_team': 'Lecce',\n", "    'away_team': 'Cagliari',\n", "    'home_team_crest': 'https://crests.football-data.org/5890.png',\n", "    'away_team_crest': 'https://crests.football-data.org/104.png'},\n", "   {'date': '2024-08-31 18:45:00',\n", "    'home_team': 'La<PERSON>',\n", "    'away_team': 'Milan',\n", "    'home_team_crest': 'https://crests.football-data.org/110.png',\n", "    'away_team_crest': 'https://crests.football-data.org/98.png'},\n", "   {'date': '2024-08-31 18:45:00',\n", "    'home_team': '<PERSON><PERSON>',\n", "    'away_team': 'Parma',\n", "    'home_team_crest': 'https://crests.football-data.org/113.png',\n", "    'away_team_crest': 'https://crests.football-data.org/112.png'},\n", "   {'date': '2024-09-01 16:30:00',\n", "    'home_team': 'Fiorentina',\n", "    'away_team': '<PERSON><PERSON>',\n", "    'home_team_crest': 'https://crests.football-data.org/99.png',\n", "    'away_team_crest': 'https://crests.football-data.org/5911.png'},\n", "   {'date': '2024-09-01 16:30:00',\n", "    'home_team': 'Genoa',\n", "    'away_team': 'Verona',\n", "    'home_team_crest': 'https://crests.football-data.org/107.png',\n", "    'away_team_crest': 'https://crests.football-data.org/450.png'},\n", "   {'date': '2024-09-01 18:45:00',\n", "    'home_team': 'Juventus',\n", "    'away_team': 'Roma',\n", "    'home_team_crest': 'https://crests.football-data.org/109.png',\n", "    'away_team_crest': 'https://crests.football-data.org/100.png'},\n", "   {'date': '2024-09-01 18:45:00',\n", "    'home_team': 'Udinese',\n", "    'away_team': '<PERSON>',\n", "    'home_team_crest': 'https://crests.football-data.org/115.png',\n", "    'away_team_crest': 'https://crests.football-data.org/7397.png'},\n", "   {'date': '2024-08-30 16:30:00',\n", "    'home_team': 'Venezia',\n", "    'away_team': 'Torino',\n", "    'home_team_crest': 'https://crests.football-data.org/454.png',\n", "    'away_team_crest': 'https://crests.football-data.org/586.png'},\n", "   {'date': '2024-08-30 18:45:00',\n", "    'home_team': 'Inter',\n", "    'away_team': 'Atalanta',\n", "    'home_team_crest': 'https://crests.football-data.org/108.png',\n", "    'away_team_crest': 'https://crests.football-data.org/102.png'},\n", "   {'date': '2024-08-31 16:30:00',\n", "    'home_team': 'Bologna',\n", "    'away_team': '<PERSON><PERSON>',\n", "    'home_team_crest': 'https://crests.football-data.org/103.png',\n", "    'away_team_crest': 'https://crests.football-data.org/445.png'},\n", "   {'date': '2024-08-31 16:30:00',\n", "    'home_team': 'Lecce',\n", "    'away_team': 'Cagliari',\n", "    'home_team_crest': 'https://crests.football-data.org/5890.png',\n", "    'away_team_crest': 'https://crests.football-data.org/104.png'},\n", "   {'date': '2024-08-31 18:45:00',\n", "    'home_team': 'La<PERSON>',\n", "    'away_team': 'Milan',\n", "    'home_team_crest': 'https://crests.football-data.org/110.png',\n", "    'away_team_crest': 'https://crests.football-data.org/98.png'},\n", "   {'date': '2024-08-31 18:45:00',\n", "    'home_team': '<PERSON><PERSON>',\n", "    'away_team': 'Parma',\n", "    'home_team_crest': 'https://crests.football-data.org/113.png',\n", "    'away_team_crest': 'https://crests.football-data.org/112.png'},\n", "   {'date': '2024-09-01 16:30:00',\n", "    'home_team': 'Fiorentina',\n", "    'away_team': '<PERSON><PERSON>',\n", "    'home_team_crest': 'https://crests.football-data.org/99.png',\n", "    'away_team_crest': 'https://crests.football-data.org/5911.png'},\n", "   {'date': '2024-09-01 16:30:00',\n", "    'home_team': 'Genoa',\n", "    'away_team': 'Verona',\n", "    'home_team_crest': 'https://crests.football-data.org/107.png',\n", "    'away_team_crest': 'https://crests.football-data.org/450.png'},\n", "   {'date': '2024-09-01 18:45:00',\n", "    'home_team': 'Juventus',\n", "    'away_team': 'Roma',\n", "    'home_team_crest': 'https://crests.football-data.org/109.png',\n", "    'away_team_crest': 'https://crests.football-data.org/100.png'},\n", "   {'date': '2024-09-01 18:45:00',\n", "    'home_team': 'Udinese',\n", "    'away_team': '<PERSON>',\n", "    'home_team_crest': 'https://crests.football-data.org/115.png',\n", "    'away_team_crest': 'https://crests.football-data.org/7397.png'}]},\n", " 'D1': {'id': 2002,\n", "  'crest': 'https://crests.football-data.org/BL1.png',\n", "  'next_matches': [{'date': '2024-08-30 18:30:00',\n", "    'home_team': 'Union Berlin',\n", "    'away_team': None,\n", "    'home_team_crest': 'https://crests.football-data.org/28.png',\n", "    'away_team_crest': 'https://crests.football-data.org/20.png'},\n", "   {'date': '2024-08-31 13:30:00',\n", "    'home_team': 'Stuttgart',\n", "    'away_team': 'Mainz',\n", "    'home_team_crest': 'https://crests.football-data.org/10.png',\n", "    'away_team_crest': 'https://crests.football-data.org/15.png'},\n", "   {'date': '2024-08-31 13:30:00',\n", "    'home_team': 'Ein Frankfurt',\n", "    'away_team': 'Hoffenheim',\n", "    'home_team_crest': 'https://crests.football-data.org/19.png',\n", "    'away_team_crest': 'https://crests.football-data.org/2.png'},\n", "   {'date': '2024-08-31 13:30:00',\n", "    'home_team': 'Werder Bremen',\n", "    'away_team': 'Dortmund',\n", "    'home_team_crest': 'https://crests.football-data.org/12.png',\n", "    'away_team_crest': 'https://crests.football-data.org/4.png'},\n", "   {'date': '2024-08-31 13:30:00',\n", "    'home_team': '<PERSON>chu<PERSON>',\n", "    'away_team': \"<PERSON><PERSON><PERSON><PERSON>\",\n", "    'home_team_crest': 'https://crests.football-data.org/36.png',\n", "    'away_team_crest': 'https://crests.football-data.org/18.png'},\n", "   {'date': '2024-08-31 13:30:00',\n", "    'home_team': None,\n", "    'away_team': 'Wolfsburg',\n", "    'home_team_crest': 'https://crests.football-data.org/720.png',\n", "    'away_team_crest': 'https://crests.football-data.org/11.png'},\n", "   {'date': '2024-08-31 16:30:00',\n", "    'home_team': 'Leverkusen',\n", "    'away_team': 'RB Leipzig',\n", "    'home_team_crest': 'https://crests.football-data.org/3.png',\n", "    'away_team_crest': 'https://crests.football-data.org/721.png'},\n", "   {'date': '2024-09-01 13:30:00',\n", "    'home_team': 'Heidenheim',\n", "    'away_team': 'Augsburg',\n", "    'home_team_crest': 'https://crests.football-data.org/44.png',\n", "    'away_team_crest': 'https://crests.football-data.org/16.png'},\n", "   {'date': '2024-09-01 15:30:00',\n", "    'home_team': 'Bayern Munich',\n", "    'away_team': 'Freiburg',\n", "    'home_team_crest': 'https://crests.football-data.org/5.png',\n", "    'away_team_crest': 'https://crests.football-data.org/17.png'},\n", "   {'date': '2024-08-30 18:30:00',\n", "    'home_team': 'Union Berlin',\n", "    'away_team': None,\n", "    'home_team_crest': 'https://crests.football-data.org/28.png',\n", "    'away_team_crest': 'https://crests.football-data.org/20.png'},\n", "   {'date': '2024-08-31 13:30:00',\n", "    'home_team': 'Stuttgart',\n", "    'away_team': 'Mainz',\n", "    'home_team_crest': 'https://crests.football-data.org/10.png',\n", "    'away_team_crest': 'https://crests.football-data.org/15.png'},\n", "   {'date': '2024-08-31 13:30:00',\n", "    'home_team': 'Ein Frankfurt',\n", "    'away_team': 'Hoffenheim',\n", "    'home_team_crest': 'https://crests.football-data.org/19.png',\n", "    'away_team_crest': 'https://crests.football-data.org/2.png'},\n", "   {'date': '2024-08-31 13:30:00',\n", "    'home_team': 'Werder Bremen',\n", "    'away_team': 'Dortmund',\n", "    'home_team_crest': 'https://crests.football-data.org/12.png',\n", "    'away_team_crest': 'https://crests.football-data.org/4.png'},\n", "   {'date': '2024-08-31 13:30:00',\n", "    'home_team': '<PERSON>chu<PERSON>',\n", "    'away_team': \"<PERSON><PERSON><PERSON><PERSON>\",\n", "    'home_team_crest': 'https://crests.football-data.org/36.png',\n", "    'away_team_crest': 'https://crests.football-data.org/18.png'},\n", "   {'date': '2024-08-31 13:30:00',\n", "    'home_team': None,\n", "    'away_team': 'Wolfsburg',\n", "    'home_team_crest': 'https://crests.football-data.org/720.png',\n", "    'away_team_crest': 'https://crests.football-data.org/11.png'},\n", "   {'date': '2024-08-31 16:30:00',\n", "    'home_team': 'Leverkusen',\n", "    'away_team': 'RB Leipzig',\n", "    'home_team_crest': 'https://crests.football-data.org/3.png',\n", "    'away_team_crest': 'https://crests.football-data.org/721.png'},\n", "   {'date': '2024-09-01 13:30:00',\n", "    'home_team': 'Heidenheim',\n", "    'away_team': 'Augsburg',\n", "    'home_team_crest': 'https://crests.football-data.org/44.png',\n", "    'away_team_crest': 'https://crests.football-data.org/16.png'},\n", "   {'date': '2024-09-01 15:30:00',\n", "    'home_team': 'Bayern Munich',\n", "    'away_team': 'Freiburg',\n", "    'home_team_crest': 'https://crests.football-data.org/5.png',\n", "    'away_team_crest': 'https://crests.football-data.org/17.png'}]},\n", " 'F1': {'id': 2015,\n", "  'crest': 'https://crests.football-data.org/FL1.png',\n", "  'next_matches': [{'date': '2024-08-30 18:45:00',\n", "    'home_team': 'Lyon',\n", "    'away_team': 'Strasbourg',\n", "    'home_team_crest': 'https://crests.football-data.org/523.png',\n", "    'away_team_crest': 'https://crests.football-data.org/576.png'},\n", "   {'date': '2024-08-31 15:00:00',\n", "    'home_team': 'Brest',\n", "    'away_team': 'St Etienne',\n", "    'home_team_crest': 'https://crests.football-data.org/512.png',\n", "    'away_team_crest': 'https://crests.football-data.org/527.png'},\n", "   {'date': '2024-08-31 17:00:00',\n", "    'home_team': 'Montpellier',\n", "    'away_team': '<PERSON><PERSON>',\n", "    'home_team_crest': 'https://crests.football-data.org/518.png',\n", "    'away_team_crest': 'https://crests.football-data.org/543.png'},\n", "   {'date': '2024-08-31 19:00:00',\n", "    'home_team': 'Toulouse',\n", "    'away_team': 'Marseille',\n", "    'home_team_crest': 'https://crests.football-data.org/511.png',\n", "    'away_team_crest': 'https://crests.football-data.org/516.png'},\n", "   {'date': '2024-09-01 13:00:00',\n", "    'home_team': 'Monaco',\n", "    'away_team': 'Lens',\n", "    'home_team_crest': 'https://crests.football-data.org/548.png',\n", "    'away_team_crest': 'https://crests.football-data.org/546.png'},\n", "   {'date': '2024-09-01 15:00:00',\n", "    'home_team': '<PERSON>s',\n", "    'away_team': 'Nice',\n", "    'home_team_crest': 'https://crests.football-data.org/532.png',\n", "    'away_team_crest': 'https://crests.football-data.org/522.png'},\n", "   {'date': '2024-09-01 15:00:00',\n", "    'home_team': '<PERSON> Havre',\n", "    'away_team': '<PERSON><PERSON><PERSON>',\n", "    'home_team_crest': 'https://crests.football-data.org/533.png',\n", "    'away_team_crest': 'https://crests.football-data.org/519.png'},\n", "   {'date': '2024-09-01 15:00:00',\n", "    'home_team': 'Reims',\n", "    'away_team': '<PERSON><PERSON>',\n", "    'home_team_crest': 'https://crests.football-data.org/547.png',\n", "    'away_team_crest': 'https://crests.football-data.org/529.png'},\n", "   {'date': '2024-09-01 18:45:00',\n", "    'home_team': 'Lille',\n", "    'away_team': 'Paris SG',\n", "    'home_team_crest': 'https://crests.football-data.org/521.png',\n", "    'away_team_crest': 'https://crests.football-data.org/524.png'},\n", "   {'date': '2024-08-30 18:45:00',\n", "    'home_team': 'Lyon',\n", "    'away_team': 'Strasbourg',\n", "    'home_team_crest': 'https://crests.football-data.org/523.png',\n", "    'away_team_crest': 'https://crests.football-data.org/576.png'},\n", "   {'date': '2024-08-31 15:00:00',\n", "    'home_team': 'Brest',\n", "    'away_team': 'St Etienne',\n", "    'home_team_crest': 'https://crests.football-data.org/512.png',\n", "    'away_team_crest': 'https://crests.football-data.org/527.png'},\n", "   {'date': '2024-08-31 17:00:00',\n", "    'home_team': 'Montpellier',\n", "    'away_team': '<PERSON><PERSON>',\n", "    'home_team_crest': 'https://crests.football-data.org/518.png',\n", "    'away_team_crest': 'https://crests.football-data.org/543.png'},\n", "   {'date': '2024-08-31 19:00:00',\n", "    'home_team': 'Toulouse',\n", "    'away_team': 'Marseille',\n", "    'home_team_crest': 'https://crests.football-data.org/511.png',\n", "    'away_team_crest': 'https://crests.football-data.org/516.png'},\n", "   {'date': '2024-09-01 13:00:00',\n", "    'home_team': 'Monaco',\n", "    'away_team': 'Lens',\n", "    'home_team_crest': 'https://crests.football-data.org/548.png',\n", "    'away_team_crest': 'https://crests.football-data.org/546.png'},\n", "   {'date': '2024-09-01 15:00:00',\n", "    'home_team': '<PERSON>s',\n", "    'away_team': 'Nice',\n", "    'home_team_crest': 'https://crests.football-data.org/532.png',\n", "    'away_team_crest': 'https://crests.football-data.org/522.png'},\n", "   {'date': '2024-09-01 15:00:00',\n", "    'home_team': '<PERSON> Havre',\n", "    'away_team': '<PERSON><PERSON><PERSON>',\n", "    'home_team_crest': 'https://crests.football-data.org/533.png',\n", "    'away_team_crest': 'https://crests.football-data.org/519.png'},\n", "   {'date': '2024-09-01 15:00:00',\n", "    'home_team': 'Reims',\n", "    'away_team': '<PERSON><PERSON>',\n", "    'home_team_crest': 'https://crests.football-data.org/547.png',\n", "    'away_team_crest': 'https://crests.football-data.org/529.png'},\n", "   {'date': '2024-09-01 18:45:00',\n", "    'home_team': 'Lille',\n", "    'away_team': 'Paris SG',\n", "    'home_team_crest': 'https://crests.football-data.org/521.png',\n", "    'away_team_crest': 'https://crests.football-data.org/524.png'}]}}"]}, "execution_count": 44, "metadata": {}, "output_type": "execute_result"}], "source": ["# Apply the function\n", "next_matches_fd_couk_format = replace_team_names(COMPETITIONS, team_name_mapping)\n", "next_matches_fd_couk_format"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [], "source": ["with open('next_matches.json', 'w') as json_file:\n", "    json.dump(next_matches_fd_couk_format, json_file, indent=4)"]}], "metadata": {"kernelspec": {"display_name": "aifootball_predictions", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 2}