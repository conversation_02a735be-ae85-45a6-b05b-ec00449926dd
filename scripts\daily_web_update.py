#!/usr/bin/env python3
"""
Daily Web Update Script for AI Football Predictions
Automatically updates matches and generates predictions for the web interface
"""

import os
import sys
import json
import subprocess
import logging
from datetime import datetime, timedelta
import argparse

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/daily_update.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class DailyWebUpdater:
    def __init__(self):
        self.base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        self.scripts_dir = os.path.join(self.base_dir, 'scripts')
        self.web_dir = os.path.join(self.base_dir, 'web')
        self.data_dir = os.path.join(self.base_dir, 'data')
        
        # Ensure directories exist
        os.makedirs(os.path.join(self.base_dir, 'logs'), exist_ok=True)
        os.makedirs(self.data_dir, exist_ok=True)
        os.makedirs(self.web_dir, exist_ok=True)
    
    def run_script(self, script_name, args=None):
        """Run a Python script and return success status"""
        if args is None:
            args = []
        
        script_path = os.path.join(self.scripts_dir, f"{script_name}.py")
        
        if not os.path.exists(script_path):
            logger.error(f"Script {script_name} not found at {script_path}")
            return False
        
        try:
            cmd = [sys.executable, script_path] + args
            logger.info(f"Running: {' '.join(cmd)}")
            
            result = subprocess.run(
                cmd,
                cwd=self.base_dir,
                capture_output=True,
                text=True,
                timeout=1800  # 30 minutes timeout
            )
            
            if result.returncode == 0:
                logger.info(f"✅ {script_name} completed successfully")
                if result.stdout:
                    logger.info(f"Output: {result.stdout}")
                return True
            else:
                logger.error(f"❌ {script_name} failed with return code {result.returncode}")
                if result.stderr:
                    logger.error(f"Error: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            logger.error(f"❌ {script_name} timed out after 30 minutes")
            return False
        except Exception as e:
            logger.error(f"❌ Error running {script_name}: {str(e)}")
            return False
    
    def clean_old_predictions(self):
        """Clean old prediction files"""
        try:
            predictions_file = os.path.join(self.web_dir, 'predictions.json')
            if os.path.exists(predictions_file):
                # Check if file is older than 1 day
                file_time = datetime.fromtimestamp(os.path.getmtime(predictions_file))
                if datetime.now() - file_time > timedelta(days=1):
                    os.remove(predictions_file)
                    logger.info("🗑️ Removed old predictions file")
            
            # Clean old match files
            next_matches_file = os.path.join(self.data_dir, 'next_matches.json')
            if os.path.exists(next_matches_file):
                file_time = datetime.fromtimestamp(os.path.getmtime(next_matches_file))
                if datetime.now() - file_time > timedelta(days=1):
                    os.remove(next_matches_file)
                    logger.info("🗑️ Removed old matches file")
                    
        except Exception as e:
            logger.error(f"Error cleaning old files: {str(e)}")
    
    def update_matches(self):
        """Update upcoming matches"""
        logger.info("🔄 Starting matches update...")
        
        # Try both methods
        success = False
        
        # Method 1: Use acquire_next_matches.py
        if self.run_script('acquire_next_matches', [
            '--output_file', os.path.join(self.data_dir, 'next_matches.json')
        ]):
            success = True
        
        # Method 2: Use update_matches.py as fallback
        if not success:
            logger.info("Trying alternative update method...")
            success = self.run_script('update_matches', [
                '--output_file', os.path.join(self.data_dir, 'next_matches.json')
            ])
        
        if success:
            logger.info("✅ Matches updated successfully")
        else:
            logger.error("❌ Failed to update matches")
        
        return success
    
    def generate_predictions(self):
        """Generate predictions for today's matches"""
        logger.info("🧠 Starting predictions generation...")
        
        success = self.run_script('make_predictions')
        
        if success:
            logger.info("✅ Predictions generated successfully")
        else:
            logger.error("❌ Failed to generate predictions")
        
        return success
    
    def validate_predictions(self):
        """Validate generated predictions"""
        try:
            predictions_file = os.path.join(self.web_dir, 'predictions.json')
            
            if not os.path.exists(predictions_file):
                logger.warning("⚠️ Predictions file not found")
                return False
            
            with open(predictions_file, 'r', encoding='utf-8') as f:
                predictions = json.load(f)
            
            if not predictions:
                logger.warning("⚠️ Predictions file is empty")
                return False
            
            total_matches = 0
            for league_data in predictions.values():
                if isinstance(league_data, dict) and 'next_matches' in league_data:
                    total_matches += len(league_data['next_matches'])
            
            logger.info(f"✅ Validation passed: {total_matches} matches with predictions")
            return True
            
        except Exception as e:
            logger.error(f"❌ Validation failed: {str(e)}")
            return False
    
    def create_status_report(self, success_steps):
        """Create a status report"""
        try:
            report = {
                "last_update": datetime.now().isoformat(),
                "success_steps": success_steps,
                "total_steps": 3,
                "success_rate": len(success_steps) / 3 * 100
            }
            
            status_file = os.path.join(self.web_dir, 'update_status.json')
            with open(status_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            logger.info(f"📊 Status report created: {len(success_steps)}/3 steps successful")
            
        except Exception as e:
            logger.error(f"Error creating status report: {str(e)}")
    
    def run_daily_update(self):
        """Run the complete daily update process"""
        logger.info("🚀 Starting daily web update process...")
        start_time = datetime.now()
        
        success_steps = []
        
        # Step 1: Clean old files
        logger.info("📋 Step 1: Cleaning old files...")
        self.clean_old_predictions()
        
        # Step 2: Update matches
        logger.info("📋 Step 2: Updating matches...")
        if self.update_matches():
            success_steps.append("update_matches")
        
        # Step 3: Generate predictions
        logger.info("📋 Step 3: Generating predictions...")
        if self.generate_predictions():
            success_steps.append("generate_predictions")
        
        # Step 4: Validate predictions
        logger.info("📋 Step 4: Validating predictions...")
        if self.validate_predictions():
            success_steps.append("validate_predictions")
        
        # Create status report
        self.create_status_report(success_steps)
        
        # Summary
        end_time = datetime.now()
        duration = end_time - start_time
        
        logger.info("=" * 50)
        logger.info("📊 DAILY UPDATE SUMMARY")
        logger.info("=" * 50)
        logger.info(f"⏱️ Duration: {duration}")
        logger.info(f"✅ Successful steps: {len(success_steps)}/3")
        logger.info(f"📈 Success rate: {len(success_steps)/3*100:.1f}%")
        
        if len(success_steps) == 3:
            logger.info("🎉 Daily update completed successfully!")
            return True
        else:
            logger.warning("⚠️ Daily update completed with some failures")
            return False

def main():
    parser = argparse.ArgumentParser(description="Daily Web Update for AI Football Predictions")
    parser.add_argument('--force', action='store_true', help='Force update even if recent data exists')
    parser.add_argument('--matches-only', action='store_true', help='Update matches only')
    parser.add_argument('--predictions-only', action='store_true', help='Generate predictions only')
    
    args = parser.parse_args()
    
    updater = DailyWebUpdater()
    
    if args.matches_only:
        logger.info("🎯 Running matches update only...")
        success = updater.update_matches()
    elif args.predictions_only:
        logger.info("🎯 Running predictions generation only...")
        success = updater.generate_predictions()
    else:
        logger.info("🎯 Running full daily update...")
        success = updater.run_daily_update()
    
    if success:
        logger.info("✅ Process completed successfully")
        sys.exit(0)
    else:
        logger.error("❌ Process completed with errors")
        sys.exit(1)

if __name__ == "__main__":
    main()
