// تنظيم التنبؤات حسب الدوريات
const leagueCategories = {
    major_european: ['E0', 'E1', 'SP1', 'I1', 'D1', 'F1'],
    arab: ['SAU', 'EGY', 'UAE', 'QAT', 'TUN', 'MAR'],
    other_european: ['N1', 'P1', 'B1', 'G1', 'T1', 'SC0', 'SC1'],
    asian: ['JPN', 'KOR', 'CHN']
};

const leagueNames = {
    'E0': 'الدوري الإنجليزي الممتاز',
    'E1': 'دوري البطولة الإنجليزية',
    'SP1': 'الدوري الإسباني',
    'I1': 'الدوري الإيطالي',
    'D1': 'الدوري الألماني',
    'F1': 'الدوري الفرنسي',
    'SAU': 'الدوري السعودي',
    'EGY': 'الدوري المصري',
    'UAE': 'الدوري الإماراتي',
    'QAT': 'الدوري القطري',
    'TUN': 'الدوري التونسي',
    'MAR': 'الدوري المغربي',
    'N1': 'الدوري الهولندي',
    'P1': 'الدوري البرتغالي',
    'B1': 'الدوري البلجيكي',
    'G1': 'الدوري اليوناني',
    'T1': 'الدوري التركي',
    'JPN': 'الدوري الياباني',
    'KOR': 'الدوري الكوري',
    'CHN': 'الدوري الصيني'
};

function createMatchElement(match) {
    const matchDiv = document.createElement('div');
    matchDiv.className = 'match';
    
    const predictionClass = match.prediction === 'over' ? 'over' : 'under';
    const predictionText = match.prediction === 'over' ? 'أكثر' : 'أقل';
    const predictionIcon = match.prediction === 'over' ? '🔥' : '❄️';
    
    matchDiv.innerHTML = `
        <div class="d-flex justify-content-between align-items-center">
            <div class="team-names">
                <strong>${match.home}</strong> 🆚 <strong>${match.away}</strong>
            </div>
            <div class="prediction ${predictionClass}">
                ${predictionIcon} ${predictionText} من 2.5 (${match.confidence}%)
            </div>
        </div>
    `;
    
    return matchDiv;
}

function displayLeagueMatches(leagueData, containerId) {
    const container = document.getElementById(containerId);
    if (!container) return;

    Object.entries(leagueData).forEach(([leagueId, matches]) => {
        if (matches.length === 0) return;

        const leagueDiv = document.createElement('div');
        leagueDiv.className = 'league-group mb-4';
        
        leagueDiv.innerHTML = `
            <h3 class="h5 mb-3">${leagueNames[leagueId] || leagueId}</h3>
        `;

        matches.forEach(match => {
            leagueDiv.appendChild(createMatchElement(match));
        });

        container.appendChild(leagueDiv);
    });
}

// تحميل وعرض التنبؤات
async function loadPredictions() {
    try {
        const response = await fetch('predictions.json');
        const predictions = await response.json();
        displayPredictions(predictions);
        updateTimestamp(predictions.timestamp);
    } catch (error) {
        console.error('Error loading predictions:', error);
    }
}

function updateTimestamp(timestamp) {
    const dateStr = new Date(timestamp).toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
    document.getElementById('update-date').textContent = dateStr;
}

function displayPredictions(predictions) {
    const sections = {
        'major-leagues': predictions.leagues.major_european,
        'arab-leagues': predictions.leagues.arab,
        'other-european-leagues': predictions.leagues.other_european,
        'asian-leagues': predictions.leagues.asian
    };

    for (const [sectionId, leagues] of Object.entries(sections)) {
        const sectionElement = document.getElementById(sectionId);
        sectionElement.innerHTML = '';

        for (const [leagueCode, league] of Object.entries(leagues)) {
            if (league.matches.length > 0) {
                const leagueGroup = document.createElement('div');
                leagueGroup.className = 'league-group';

                const leagueHeader = document.createElement('h3');
                leagueHeader.className = 'mb-3';
                leagueHeader.textContent = league.name;
                leagueGroup.appendChild(leagueHeader);

                league.matches.forEach(match => {
                    const matchDiv = document.createElement('div');
                    matchDiv.className = 'match';

                    const teamsDiv = document.createElement('div');
                    teamsDiv.className = 'team-names mb-2';
                    teamsDiv.textContent = `${match.home} 🆚 ${match.away}`;

                    const predictionDiv = document.createElement('div');
                    predictionDiv.className = `prediction ${match.prediction === 'over' ? 'over' : 'under'}`;
                    
                    const predictionText = match.prediction === 'over' ? 
                        '⚽ أكثر من 2.5 هدف' : 
                        '⛔ أقل من 2.5 هدف';
                    
                    predictionDiv.innerHTML = `
                        ${predictionText}
                        <span class="confidence ms-2">(${match.confidence.toFixed(1)}%)</span>
                    `;

                    matchDiv.appendChild(teamsDiv);
                    matchDiv.appendChild(predictionDiv);
                    leagueGroup.appendChild(matchDiv);
                });

                sectionElement.appendChild(leagueGroup);
            }
        }

        if (sectionElement.children.length === 0) {
            sectionElement.innerHTML = '<p class="text-muted">لا توجد مباريات متوقعة حالياً</p>';
        }
    }
}

document.addEventListener('DOMContentLoaded', loadPredictions);
