{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["### Data Preprocessing\n", "\n", "### Overview\n", "\n", "This notebook is used to make the over 2.5 predictions for all the matches saved in the `data/netx_matches.json` file. \n", "\n", "### Pre-requisites \n", "\n", "1. A conda environment is needed.\n", "\n", "For example:\n", "```\n", "cd path/to/conda/dir\n", "conda env create -f aifootball_predictions.yaml\n", "conda activate aifootball_predictions\n", "python -m ipykernel install --user --name aifootball_predictions --display-name \"aifootball_predictions\"\n", "```\n", "\n", "2. All the trained models in the `models` directory\n", "\n", "3. All the preprocessed dataset `data/preprocessed` which will be used to make the predictions\n", "\n", "### Authors\n", "\n", "- <EMAIL>"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["# install the necessary packages\n", "import pandas as pd\n", "import os\n", "import json\n", "import pickle\n", "import numpy as np\n", "from datetime import datetime\n", "from sklearn.impute import KNNImputer"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Specify useful features for the home and away teams, togheter with the netural features (related to no team)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["home_team_features = [\n", "    'HomeTeam',\n", "    'FTHG', 'HG',  # Full Time Home Team Goals\n", "    'HTHG',        # Half Time Home Team Goals\n", "    'HS',          # Home Team Shots\n", "    'HST',         # Home Team Shots on Target\n", "    'HHW',         # Home Team Hit Woodwork\n", "    'HC',          # Home Team Corners\n", "    'HF',          # Home Team Fouls Committed\n", "    'HFK<PERSON>',        # Home Team Free Kicks Conceded\n", "    'HO',          # Home Team Offsides\n", "    'HY',          # Home Team Yellow Cards\n", "    'HR',          # Home Team Red Cards\n", "    'HBP',         # Home Team Bookings Points\n", "    'B365H', 'BFH', 'BSH', 'BWH', 'GBH', 'IWH', 'LBH', 'PSH', 'SOH', 'SBH', 'SJH', 'SYH', 'VCH', 'WHH',  # Home win odds\n", "    'BbMxH', 'BbAvH', 'MaxH', 'AvgH',  # Home win odds statistics\n", "    'BFEH',       # Betfair Exchange home win odds\n", "    'BbMxAHH', 'BbAvAHH', 'GBAH<PERSON>', 'LBAHH', 'B365AHH', 'PAHH', 'MaxAHH', 'AvgAHH',  # Asian handicap home team odds\n", "    'BbAHh', 'AHh', 'GBAH', 'LBAH', 'B365AH',  # Size of handicap (home team)\n", "    'AvgHomeGoalsScored', 'AvgHomeGoalsConceded',\n", "    'HomeOver2.5Perc',\n", "    'AvgLast5HomeGoalsScored', 'AvgLast5HomeGoalsConceded',\n", "    'Last5HomeOver2.5Count', 'Last5HomeOver2.5Perc'\n", "]\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["away_team_features = [\n", "    'AwayTeam',\n", "    'FTAG', 'AG',  # Full Time Away Team Goals\n", "    'HTAG',        # Half Time Away Team Goals\n", "    'AS',          # Away Team Shots\n", "    'AST',         # Away Team Shots on Target\n", "    'AHW',         # Away Team Hit Woodwork\n", "    'AC',          # Away Team Corners\n", "    'AF',          # Away Team Fouls Committed\n", "    'AFKC',        # Away Team Free Kicks Conceded\n", "    'AO',          # Away Team Offsides\n", "    'AY',          # Away Team Yellow Cards\n", "    'AR',          # Away Team Red Cards\n", "    'ABP',         # Away Team Bookings Points\n", "    'B365A', 'BFA', 'BSA', 'BWA', 'GBA', 'IWA', 'LBA', 'PSA', 'SOA', 'SBA', 'SJA', 'SYA', 'VCA', 'WHA',  # Away win odds\n", "    'BbMxA', 'BbAvA', 'MaxA', 'AvgA',  # Away win odds statistics\n", "    'BFEA',       # Betfair Exchange away win odds\n", "    'BbMxAHA', 'BbAvAHA', 'GBAHA', 'LBAHA', 'B365AHA', 'PAHA', 'MaxAHA', 'AvgAHA',  # Asian handicap away team odds\n", "    'AvgAwayGoalsScored', 'AvgAwayGoalsConceded',\n", "    'AwayOver2.5Perc',\n", "    'AvgLast5AwayGoalsScored', 'AvgLast5AwayGoalsConceded',\n", "    'Last5AwayOver2.5Count', 'Last5AwayOver2.5Perc'\n", "]"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["general_features = [\n", "    'Div',       # League Division\n", "    'Date',      # Match Date\n", "    'Time',      # Match Time\n", "    'F<PERSON>', 'Res',  # Full Time Result\n", "    'H<PERSON>',        # Half Time Result\n", "    'Attendance', # Crowd Attendance\n", "    'Referee',    # Match Referee\n", "    'Bb1X2',      # Number of BetBrain bookmakers used to calculate match odds averages and maximums\n", "    'BbMxD', 'BbAvD', 'MaxD', 'AvgD',  # Draw odds statistics\n", "    'B365D', 'BFD', 'BSD', 'BWD', 'GBD', 'IWD', 'LBD', 'PSD', 'SOD', 'SBD', 'SJD', 'SYD', 'VCD', 'WHD',  # Draw odds\n", "    'BbOU',       # Number of BetBrain bookmakers used to calculate over/under 2.5 goals\n", "    'BbMx>2.5', 'BbAv>2.5', 'BbMx<2.5', 'BbAv<2.5',  # Over/Under 2.5 goals odds statistics\n", "    'GB>2.5', 'GB<2.5', 'B365>2.5', 'B365<2.5', 'P>2.5', 'P<2.5', 'Max>2.5', 'Max<2.5', 'Avg>2.5', 'AvgC>2.5', 'Avg<2.5', 'AvgC<2.5', 'MaxCAHA', 'B365CD', 'PC<2.5',\n", "    'MaxC>2.5', 'B365C<2.5',  'MaxCA', 'B365CAHH',# Over/Under 2.5 goals odds\n", "    'BbAH',       # Number of BetBrain bookmakers used to Asian handicap averages and maximums\n", "    'Over2.5'     # Binary indicator if the match ended with more than 2.5 total goals\n", "]"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# Open the JSON file and load its data into a dictionary\n", "with open(\"../data/next_matches.json\", 'r', encoding='utf-16') as json_file:\n", "    data_dict = json.load(json_file)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'E0': {'id': 2021,\n", "  'crest': 'https://crests.football-data.org/PL.png',\n", "  'name': 'Premier League',\n", "  'next_matches': [{'date': '2024-09-14 11:30:00',\n", "    'home_team': 'Southampton',\n", "    'away_team': 'Man United',\n", "    'home_team_crest': 'https://crests.football-data.org/340.png',\n", "    'away_team_crest': 'https://crests.football-data.org/66.png'},\n", "   {'date': '2024-09-14 14:00:00',\n", "    'home_team': 'Brighton',\n", "    'away_team': 'Ipswich',\n", "    'home_team_crest': 'https://crests.football-data.org/397.png',\n", "    'away_team_crest': 'https://crests.football-data.org/349.png'},\n", "   {'date': '2024-09-14 14:00:00',\n", "    'home_team': 'Crystal Palace',\n", "    'away_team': 'Leicester',\n", "    'home_team_crest': 'https://crests.football-data.org/354.png',\n", "    'away_team_crest': 'https://crests.football-data.org/338.png'},\n", "   {'date': '2024-09-14 14:00:00',\n", "    'home_team': 'Fulham',\n", "    'away_team': 'West Ham',\n", "    'home_team_crest': 'https://crests.football-data.org/63.png',\n", "    'away_team_crest': 'https://crests.football-data.org/563.png'},\n", "   {'date': '2024-09-14 14:00:00',\n", "    'home_team': 'Liverpool',\n", "    'away_team': \"Nott'm Forest\",\n", "    'home_team_crest': 'https://crests.football-data.org/64.png',\n", "    'away_team_crest': 'https://crests.football-data.org/351.png'},\n", "   {'date': '2024-09-14 14:00:00',\n", "    'home_team': 'Man City',\n", "    'away_team': 'Brentford',\n", "    'home_team_crest': 'https://crests.football-data.org/65.png',\n", "    'away_team_crest': 'https://crests.football-data.org/402.png'},\n", "   {'date': '2024-09-14 16:30:00',\n", "    'home_team': 'Aston Villa',\n", "    'away_team': 'Everton',\n", "    'home_team_crest': 'https://crests.football-data.org/58.png',\n", "    'away_team_crest': 'https://crests.football-data.org/62.png'},\n", "   {'date': '2024-09-14 19:00:00',\n", "    'home_team': 'Bournemouth',\n", "    'away_team': '<PERSON>',\n", "    'home_team_crest': 'https://crests.football-data.org/bournemouth.png',\n", "    'away_team_crest': 'https://crests.football-data.org/61.png'},\n", "   {'date': '2024-09-15 13:00:00',\n", "    'home_team': 'Tottenham',\n", "    'away_team': 'Arsenal',\n", "    'home_team_crest': 'https://crests.football-data.org/73.png',\n", "    'away_team_crest': 'https://crests.football-data.org/57.png'},\n", "   {'date': '2024-09-15 15:30:00',\n", "    'home_team': 'Wolves',\n", "    'away_team': 'Newcastle',\n", "    'home_team_crest': 'https://crests.football-data.org/76.png',\n", "    'away_team_crest': 'https://crests.football-data.org/67.png'}]},\n", " 'SP1': {'id': 2014,\n", "  'crest': 'https://crests.football-data.org/PD.png',\n", "  'name': 'La Liga',\n", "  'next_matches': [{'date': '2024-09-13 19:00:00',\n", "    'home_team': 'Betis',\n", "    'away_team': '<PERSON>gan<PERSON>',\n", "    'home_team_crest': 'https://crests.football-data.org/90.png',\n", "    'away_team_crest': 'https://crests.football-data.org/745.png'},\n", "   {'date': '2024-09-14 12:00:00',\n", "    'home_team': 'Mallorca',\n", "    'away_team': '<PERSON><PERSON><PERSON>',\n", "    'home_team_crest': 'https://crests.football-data.org/89.png',\n", "    'away_team_crest': 'https://crests.football-data.org/94.png'},\n", "   {'date': '2024-09-14 14:15:00',\n", "    'home_team': 'Espanol',\n", "    'away_team': '<PERSON><PERSON>',\n", "    'home_team_crest': 'https://crests.football-data.org/80.png',\n", "    'away_team_crest': 'https://crests.football-data.org/263.png'},\n", "   {'date': '2024-09-14 16:30:00',\n", "    'home_team': 'Sevilla',\n", "    'away_team': 'Getafe',\n", "    'home_team_crest': 'https://crests.football-data.org/559.png',\n", "    'away_team_crest': 'https://crests.football-data.org/82.png'},\n", "   {'date': '2024-09-14 19:00:00',\n", "    'home_team': 'Sociedad',\n", "    'away_team': 'Real Madrid',\n", "    'home_team_crest': 'https://crests.football-data.org/92.png',\n", "    'away_team_crest': 'https://crests.football-data.org/86.png'},\n", "   {'date': '2024-09-15 12:00:00',\n", "    'home_team': '<PERSON><PERSON>',\n", "    'away_team': '<PERSON><PERSON><PERSON><PERSON>',\n", "    'home_team_crest': 'https://crests.football-data.org/558.png',\n", "    'away_team_crest': 'https://crests.football-data.org/250.png'},\n", "   {'date': '2024-09-15 14:15:00',\n", "    'home_team': 'Girona',\n", "    'away_team': 'Barcelona',\n", "    'home_team_crest': 'https://crests.football-data.org/298.png',\n", "    'away_team_crest': 'https://crests.football-data.org/81.png'},\n", "   {'date': '2024-09-15 16:30:00',\n", "    'home_team': 'Las Palmas',\n", "    'away_team': 'Ath Bilbao',\n", "    'home_team_crest': 'https://crests.football-data.org/275.png',\n", "    'away_team_crest': 'https://crests.football-data.org/77.png'},\n", "   {'date': '2024-09-15 19:00:00',\n", "    'home_team': 'Ath Madrid',\n", "    'away_team': 'Valencia',\n", "    'home_team_crest': 'https://crests.football-data.org/78.png',\n", "    'away_team_crest': 'https://crests.football-data.org/95.png'},\n", "   {'date': '2024-09-16 19:00:00',\n", "    'home_team': 'Vallecano',\n", "    'away_team': '<PERSON><PERSON><PERSON><PERSON>',\n", "    'home_team_crest': 'https://crests.football-data.org/87.png',\n", "    'away_team_crest': 'https://crests.football-data.org/79.png'}]},\n", " 'I1': {'id': 2019,\n", "  'crest': 'https://crests.football-data.org/SA.png',\n", "  'name': 'Serie A',\n", "  'next_matches': [{'date': '2024-09-14 13:00:00',\n", "    'home_team': '<PERSON>',\n", "    'away_team': 'Bologna',\n", "    'home_team_crest': 'https://crests.football-data.org/7397.png',\n", "    'away_team_crest': 'https://crests.football-data.org/103.png'},\n", "   {'date': '2024-09-14 16:00:00',\n", "    'home_team': '<PERSON><PERSON>',\n", "    'away_team': 'Juventus',\n", "    'home_team_crest': 'https://crests.football-data.org/445.png',\n", "    'away_team_crest': 'https://crests.football-data.org/109.png'},\n", "   {'date': '2024-09-14 18:45:00',\n", "    'home_team': 'Milan',\n", "    'away_team': 'Venezia',\n", "    'home_team_crest': 'https://crests.football-data.org/98.png',\n", "    'away_team_crest': 'https://crests.football-data.org/454.png'},\n", "   {'date': '2024-09-15 10:30:00',\n", "    'home_team': 'Genoa',\n", "    'away_team': 'Roma',\n", "    'home_team_crest': 'https://crests.football-data.org/107.png',\n", "    'away_team_crest': 'https://crests.football-data.org/100.png'},\n", "   {'date': '2024-09-15 13:00:00',\n", "    'home_team': 'Atalanta',\n", "    'away_team': 'Fiorentina',\n", "    'home_team_crest': 'https://crests.football-data.org/102.png',\n", "    'away_team_crest': 'https://crests.football-data.org/99.png'},\n", "   {'date': '2024-09-15 13:00:00',\n", "    'home_team': 'Torino',\n", "    'away_team': 'Lecce',\n", "    'home_team_crest': 'https://crests.football-data.org/586.png',\n", "    'away_team_crest': 'https://crests.football-data.org/5890.png'},\n", "   {'date': '2024-09-15 16:00:00',\n", "    'home_team': 'Cagliari',\n", "    'away_team': '<PERSON><PERSON>',\n", "    'home_team_crest': 'https://crests.football-data.org/104.png',\n", "    'away_team_crest': 'https://crests.football-data.org/113.png'},\n", "   {'date': '2024-09-15 18:45:00',\n", "    'home_team': '<PERSON><PERSON>',\n", "    'away_team': 'Inter',\n", "    'home_team_crest': 'https://crests.football-data.org/5911.png',\n", "    'away_team_crest': 'https://crests.football-data.org/108.png'},\n", "   {'date': '2024-09-16 16:30:00',\n", "    'home_team': 'Parma',\n", "    'away_team': 'Udinese',\n", "    'home_team_crest': 'https://crests.football-data.org/112.png',\n", "    'away_team_crest': 'https://crests.football-data.org/115.png'},\n", "   {'date': '2024-09-16 18:45:00',\n", "    'home_team': 'La<PERSON>',\n", "    'away_team': 'Verona',\n", "    'home_team_crest': 'https://crests.football-data.org/110.png',\n", "    'away_team_crest': 'https://crests.football-data.org/450.png'}]},\n", " 'D1': {'id': 2002,\n", "  'crest': 'https://crests.football-data.org/BL1.png',\n", "  'name': 'Bundesliga',\n", "  'next_matches': [{'date': '2024-09-13 18:30:00',\n", "    'home_team': 'Dortmund',\n", "    'away_team': 'Heiden<PERSON>',\n", "    'home_team_crest': 'https://crests.football-data.org/4.png',\n", "    'away_team_crest': 'https://crests.football-data.org/44.png'},\n", "   {'date': '2024-09-14 13:30:00',\n", "    'home_team': 'RB Leipzig',\n", "    'away_team': 'Union Berlin',\n", "    'home_team_crest': 'https://crests.football-data.org/721.png',\n", "    'away_team_crest': 'https://crests.football-data.org/28.png'},\n", "   {'date': '2024-09-14 13:30:00',\n", "    'home_team': 'Hoffenheim',\n", "    'away_team': 'Leverkusen',\n", "    'home_team_crest': 'https://crests.football-data.org/2.png',\n", "    'away_team_crest': 'https://crests.football-data.org/3.png'},\n", "   {'date': '2024-09-14 13:30:00',\n", "    'home_team': 'Freiburg',\n", "    'away_team': '<PERSON><PERSON><PERSON>',\n", "    'home_team_crest': 'https://crests.football-data.org/17.png',\n", "    'away_team_crest': 'https://crests.football-data.org/36.png'},\n", "   {'date': '2024-09-14 13:30:00',\n", "    'home_team': 'Wolfsburg',\n", "    'away_team': 'Ein Frankfurt',\n", "    'home_team_crest': 'https://crests.football-data.org/11.png',\n", "    'away_team_crest': 'https://crests.football-data.org/19.png'},\n", "   {'date': '2024-09-14 13:30:00',\n", "    'home_team': \"<PERSON><PERSON><PERSON><PERSON>\",\n", "    'away_team': 'Stuttgart',\n", "    'home_team_crest': 'https://crests.football-data.org/18.png',\n", "    'away_team_crest': 'https://crests.football-data.org/10.png'},\n", "   {'date': '2024-09-14 16:30:00',\n", "    'home_team': 'Holstein Kiel',\n", "    'away_team': 'Bayern Munich',\n", "    'home_team_crest': 'https://crests.football-data.org/720.png',\n", "    'away_team_crest': 'https://crests.football-data.org/5.png'},\n", "   {'date': '2024-09-15 13:30:00',\n", "    'home_team': 'Augsburg',\n", "    'away_team': 'St Pauli',\n", "    'home_team_crest': 'https://crests.football-data.org/16.png',\n", "    'away_team_crest': 'https://crests.football-data.org/20.png'},\n", "   {'date': '2024-09-15 15:30:00',\n", "    'home_team': 'Mainz',\n", "    'away_team': 'Werder Bremen',\n", "    'home_team_crest': 'https://crests.football-data.org/15.png',\n", "    'away_team_crest': 'https://crests.football-data.org/12.png'}]},\n", " 'F1': {'id': 2015,\n", "  'crest': 'https://crests.football-data.org/FL1.png',\n", "  'name': 'Ligue 1',\n", "  'next_matches': [{'date': '2024-09-13 18:45:00',\n", "    'home_team': 'St Etienne',\n", "    'away_team': 'Lille',\n", "    'home_team_crest': 'https://crests.football-data.org/527.png',\n", "    'away_team_crest': 'https://crests.football-data.org/521.png'},\n", "   {'date': '2024-09-14 15:00:00',\n", "    'home_team': 'Marseille',\n", "    'away_team': 'Nice',\n", "    'home_team_crest': 'https://crests.football-data.org/516.png',\n", "    'away_team_crest': 'https://crests.football-data.org/522.png'},\n", "   {'date': '2024-09-14 17:00:00',\n", "    'home_team': '<PERSON><PERSON><PERSON>',\n", "    'away_team': 'Monaco',\n", "    'home_team_crest': 'https://crests.football-data.org/519.png',\n", "    'away_team_crest': 'https://crests.football-data.org/548.png'},\n", "   {'date': '2024-09-14 19:00:00',\n", "    'home_team': 'Paris SG',\n", "    'away_team': 'Brest',\n", "    'home_team_crest': 'https://crests.football-data.org/524.png',\n", "    'away_team_crest': 'https://crests.football-data.org/512.png'},\n", "   {'date': '2024-09-15 13:00:00',\n", "    'home_team': 'Rennes',\n", "    'away_team': 'Montpellier',\n", "    'home_team_crest': 'https://crests.football-data.org/529.png',\n", "    'away_team_crest': 'https://crests.football-data.org/518.png'},\n", "   {'date': '2024-09-15 15:00:00',\n", "    'home_team': 'Nan<PERSON>',\n", "    'away_team': 'Reims',\n", "    'home_team_crest': 'https://crests.football-data.org/543.png',\n", "    'away_team_crest': 'https://crests.football-data.org/547.png'},\n", "   {'date': '2024-09-15 15:00:00',\n", "    'home_team': 'Toulouse',\n", "    'away_team': '<PERSON> Havre',\n", "    'home_team_crest': 'https://crests.football-data.org/511.png',\n", "    'away_team_crest': 'https://crests.football-data.org/533.png'},\n", "   {'date': '2024-09-15 15:00:00',\n", "    'home_team': 'Strasbourg',\n", "    'away_team': '<PERSON>s',\n", "    'home_team_crest': 'https://crests.football-data.org/576.png',\n", "    'away_team_crest': 'https://crests.football-data.org/532.png'},\n", "   {'date': '2024-09-15 18:45:00',\n", "    'home_team': 'Lens',\n", "    'away_team': 'Lyon',\n", "    'home_team_crest': 'https://crests.football-data.org/546.png',\n", "    'away_team_crest': 'https://crests.football-data.org/523.png'}]}}"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["data_dict"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["# Define the KNN imputer\n", "knn_imputer = KNNImputer(n_neighbors=5)\n", "\n", "# Function to apply KNN imputer to league data and row_to_predict\n", "def impute_with_knn(league_data: pd.DataFrame, row_to_predict: pd.DataFrame) -> pd.DataFrame:\n", "    \"\"\"\n", "    Impute missing values in row_to_predict using KNN based on the entire league data.\n", "\n", "    Args:\n", "        league_data (pd.DataFrame): The entire dataset for the current league.\n", "        row_to_predict (pd.DataFrame): The row that needs to be predicted and imputed.\n", "\n", "    Returns:\n", "        pd.DataFrame: The imputed row.\n", "    \"\"\"\n", "    # Select only numeric columns from the league data\n", "    numeric_columns = league_data.select_dtypes(include=['number']).columns\n", "    \n", "    # Combine the row_to_predict with league data\n", "    combined_data = pd.concat([league_data[numeric_columns], row_to_predict], ignore_index=True)\n", "    \n", "    # Apply KNN imputation on the combined data\n", "    imputed_data = knn_imputer.fit_transform(combined_data)\n", "    \n", "    # Extract the last row (the imputed row_to_predict)\n", "    imputed_row = pd.DataFrame([imputed_data[-1]], columns=numeric_columns)\n", "    \n", "    return imputed_row\n"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", " Making predictions for E0 \n", "\n", "Home team: Southampton, Away team: Man United\n"]}, {"ename": "TypeError", "evalue": "cannot concatenate object of type '<class 'str'>'; only Series and DataFrame objs are valid", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mTypeError\u001b[0m                                 Trace<PERSON> (most recent call last)", "Cell \u001b[1;32mIn[23], line 101\u001b[0m\n\u001b[0;32m     98\u001b[0m         row_to_predict\u001b[38;5;241m.\u001b[39mloc[\u001b[38;5;28mlen\u001b[39m(row_to_predict)\u001b[38;5;241m-\u001b[39m\u001b[38;5;241m1\u001b[39m, column] \u001b[38;5;241m=\u001b[39m away_team_final_df[column]\u001b[38;5;241m.\u001b[39mmean()\n\u001b[0;32m     99\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m :\n\u001b[0;32m    100\u001b[0m         \u001b[38;5;66;03m#print(f\"Column in the general_features: {column}\")\u001b[39;00m\n\u001b[1;32m--> 101\u001b[0m         row_to_predict\u001b[38;5;241m.\u001b[39mloc[\u001b[38;5;28mlen\u001b[39m(row_to_predict)\u001b[38;5;241m-\u001b[39m\u001b[38;5;241m1\u001b[39m,column] \u001b[38;5;241m=\u001b[39m \u001b[43mimpute_with_knn\u001b[49m\u001b[43m(\u001b[49m\u001b[43mleague_data\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcolumn\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    103\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m    104\u001b[0m     \u001b[38;5;66;03m# Make the prediction\u001b[39;00m\n\u001b[0;32m    105\u001b[0m     X_test \u001b[38;5;241m=\u001b[39m row_to_predict\u001b[38;5;241m.\u001b[39mvalues\n", "Cell \u001b[1;32mIn[21], line 20\u001b[0m, in \u001b[0;36mimpute_with_knn\u001b[1;34m(league_data, row_to_predict)\u001b[0m\n\u001b[0;32m     17\u001b[0m numeric_columns \u001b[38;5;241m=\u001b[39m league_data\u001b[38;5;241m.\u001b[39mselect_dtypes(include\u001b[38;5;241m=\u001b[39m[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mnumber\u001b[39m\u001b[38;5;124m'\u001b[39m])\u001b[38;5;241m.\u001b[39mcolumns\n\u001b[0;32m     19\u001b[0m \u001b[38;5;66;03m# Combine the row_to_predict with league data\u001b[39;00m\n\u001b[1;32m---> 20\u001b[0m combined_data \u001b[38;5;241m=\u001b[39m \u001b[43mpd\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mconcat\u001b[49m\u001b[43m(\u001b[49m\u001b[43m[\u001b[49m\u001b[43mleague_data\u001b[49m\u001b[43m[\u001b[49m\u001b[43mnumeric_columns\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mrow_to_predict\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mignore_index\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m)\u001b[49m\n\u001b[0;32m     22\u001b[0m \u001b[38;5;66;03m# Apply KNN imputation on the combined data\u001b[39;00m\n\u001b[0;32m     23\u001b[0m imputed_data \u001b[38;5;241m=\u001b[39m knn_imputer\u001b[38;5;241m.\u001b[39mfit_transform(combined_data)\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\aifootball_predictions\\lib\\site-packages\\pandas\\core\\reshape\\concat.py:382\u001b[0m, in \u001b[0;36mconcat\u001b[1;34m(objs, axis, join, ignore_index, keys, levels, names, verify_integrity, sort, copy)\u001b[0m\n\u001b[0;32m    379\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m copy \u001b[38;5;129;01mand\u001b[39;00m using_copy_on_write():\n\u001b[0;32m    380\u001b[0m     copy \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mFalse\u001b[39;00m\n\u001b[1;32m--> 382\u001b[0m op \u001b[38;5;241m=\u001b[39m \u001b[43m_Concatenator\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    383\u001b[0m \u001b[43m    \u001b[49m\u001b[43mobjs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    384\u001b[0m \u001b[43m    \u001b[49m\u001b[43maxis\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43maxis\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    385\u001b[0m \u001b[43m    \u001b[49m\u001b[43mignore_index\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mignore_index\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    386\u001b[0m \u001b[43m    \u001b[49m\u001b[43mjoin\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mjoin\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    387\u001b[0m \u001b[43m    \u001b[49m\u001b[43mkeys\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mkeys\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    388\u001b[0m \u001b[43m    \u001b[49m\u001b[43mlevels\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mlevels\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    389\u001b[0m \u001b[43m    \u001b[49m\u001b[43mnames\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mnames\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    390\u001b[0m \u001b[43m    \u001b[49m\u001b[43mverify_integrity\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mverify_integrity\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    391\u001b[0m \u001b[43m    \u001b[49m\u001b[43mcopy\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcopy\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    392\u001b[0m \u001b[43m    \u001b[49m\u001b[43msort\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43msort\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    393\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    395\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m op\u001b[38;5;241m.\u001b[39mget_result()\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\aifootball_predictions\\lib\\site-packages\\pandas\\core\\reshape\\concat.py:448\u001b[0m, in \u001b[0;36m_Concatenator.__init__\u001b[1;34m(self, objs, axis, join, keys, levels, names, ignore_index, verify_integrity, copy, sort)\u001b[0m\n\u001b[0;32m    445\u001b[0m objs, keys \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_clean_keys_and_objs(objs, keys)\n\u001b[0;32m    447\u001b[0m \u001b[38;5;66;03m# figure out what our result ndim is going to be\u001b[39;00m\n\u001b[1;32m--> 448\u001b[0m ndims \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_get_ndims\u001b[49m\u001b[43m(\u001b[49m\u001b[43mobjs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    449\u001b[0m sample, objs \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_get_sample_object(objs, ndims, keys, names, levels)\n\u001b[0;32m    451\u001b[0m \u001b[38;5;66;03m# Standardize axis parameter to int\u001b[39;00m\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\aifootball_predictions\\lib\\site-packages\\pandas\\core\\reshape\\concat.py:489\u001b[0m, in \u001b[0;36m_Concatenator._get_ndims\u001b[1;34m(self, objs)\u001b[0m\n\u001b[0;32m    484\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(obj, (ABCSeries, ABCDataFrame)):\n\u001b[0;32m    485\u001b[0m         msg \u001b[38;5;241m=\u001b[39m (\n\u001b[0;32m    486\u001b[0m             \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcannot concatenate object of type \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mtype\u001b[39m(obj)\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m; \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    487\u001b[0m             \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124monly Series and DataFrame objs are valid\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    488\u001b[0m         )\n\u001b[1;32m--> 489\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mTypeError\u001b[39;00m(msg)\n\u001b[0;32m    491\u001b[0m     ndims\u001b[38;5;241m.\u001b[39madd(obj\u001b[38;5;241m.\u001b[39mndim)\n\u001b[0;32m    492\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m ndims\n", "\u001b[1;31mTypeError\u001b[0m: cannot concatenate object of type '<class 'str'>'; only Series and DataFrame objs are valid"]}], "source": ["# Open the JSON file and load its data into a dictionary\n", "json_competitions = '../data/next_matches.json'\n", "with open(json_competitions, 'r', encoding='utf-16') as json_file:\n", "    competitions = json.load(json_file)\n", "\n", "# Define the models directory\n", "models_dir = '../models'\n", "# Define the data directory to make predictions\n", "data_dir = '../data/processed'\n", "\n", "VALID_LEAGUES = [\"E0\",\"I1\", \"D1\", \"SP1\", \"F1\"]\n", "\n", "# Initialize a variable to hold the Telegram message\n", "prediction_message = f\"🎯 **AI Football Predictions: Will There Be Over 2.5 Goals?** 🎯\\n\\nCheck out the latest predictions for the upcoming football matches! We've analyzed the data and here are our thoughts:\\n PREDICTIONS DONE: {datetime.now().strftime('%Y-%m-%d')} \\n\\n\"\n", "\n", "for league in VALID_LEAGUES:\n", "    # Iterate through the directory to find all .pkl files\n", "    for filename in os.listdir(models_dir):\n", "        # Check if the name of the file contains the league identifier\n", "        if league in filename:\n", "            # Checl if the file is a .pkl file\n", "            if filename.endswith('.pkl'):\n", "                filepath = os.path.join(models_dir, filename)\n", "\n", "                # Load the model using pickle for the current league\n", "                with open(filepath, 'rb') as file:\n", "                    league_model = pickle.load(file)\n", "    \n", "    # Iterate through the directory to find all .csv files\n", "    for filename in os.listdir(data_dir):\n", "        # Check if the name of the file contains the league identifier\n", "        if league in filename:\n", "            # Check if the file is a .csv file\n", "            if filename.endswith('.csv'):\n", "                filepath = os.path.join(data_dir, filename)\n", "                # Load the data using pandas for the current league\n", "                league_data = pd.read_csv(filepath)\n", "\n", "    # If the input_league_data and league_model is not empty, make the predictions\n", "    if league_data is None and league_model is None:\n", "        print(f\"Could not find the data or model for the league {league}\")\n", "        continue\n", "    # else make the predictions\n", "    else:    \n", "        # Make the predictions\n", "        for competition_league, competitions_info in competitions.items():\n", "            if competition_league == league:\n", "                # Define the nationality flag to use in the message\n", "                print(f\"\\n Making predictions for {league} \\n\")\n", "                # Prepare the section for the current league\n", "                league_section = f\"🔵 **{competitions_info['name']}**:\\n\"\n", "                for match in competitions_info[\"next_matches\"]:            \n", "                    home_team = match['home_team']\n", "                    away_team = match['away_team']\n", "                    print(f\"Home team: {home_team}, Away team: {away_team}\")\n", "\n", "                    # row to predict\n", "                    numeric_columns = league_data.select_dtypes(include=['number']).columns  # Get the column names for all the numeric columns\n", "\n", "                    # If the column Over2.5 is in the numeric columns, remove it because it is the target column\n", "                    if 'Over2.5' in numeric_columns:\n", "                        numeric_columns = numeric_columns.drop('Over2.5')\n", "\n", "                    # Define the row to predict \n", "                    row_to_predict = pd.DataFrame(columns=numeric_columns)  # Create an empty DataFrame with the numeric columns\n", "                    row_to_predict.loc[len(row_to_predict)] = [None] * len(row_to_predict.columns)  # Initialize a new row with NaN\n", "\n", "                    # Check if the home team is in the DataFrame\n", "                    if home_team not in league_data['HomeTeam'].values:\n", "                        print(f\"Home team {home_team} not found in the {league} data, skipping to the next match\")\n", "                        continue\n", "\n", "                    # Check if the away team is in the DataFrame\n", "                    if away_team not in league_data['AwayTeam'].values:\n", "                        print(f\"Away team {away_team} not found in the {league} data, skipping to the next match\")\n", "                        continue\n", "\n", "                    # get the data for the home team\n", "                    home_team_df = league_data[league_data['HomeTeam'] == home_team]\n", "                    # Sort the filtered DataFrame by Date in descending order\n", "                    home_sorted_df = home_team_df.sort_values(by='Date', ascending=False)\n", "                    # Select the first 5 rows\n", "                    home_team_final_df = home_sorted_df.head(5)[numeric_columns]\n", "\n", "                    # get the data for the home team\n", "                    away_team_df = league_data[league_data['AwayTeam'] == away_team]\n", "                    # Sort the filtered DataFrame by Date in descending order\n", "                    away_sorted_df = away_team_df.sort_values(by='Date', ascending=False)\n", "                    # Select the first 5 rows\n", "                    away_team_final_df = away_sorted_df.head(5)[numeric_columns]\n", "\n", "                    for column in row_to_predict.columns:\n", "                        if column in home_team_features:\n", "                            #print(f\"Column in the home_team_df: {column}\")\n", "                            row_to_predict.loc[len(row_to_predict)-1,column] = home_team_final_df[column].mean()\n", "                        elif column in away_team_features:\n", "                            #print(f\"Column in the away_team_df: {column}\")\n", "                            row_to_predict.loc[len(row_to_predict)-1, column] = away_team_final_df[column].mean()\n", "                        else :\n", "                            #print(f\"Column in the general_features: {column}\")\n", "                            row_to_predict.loc[len(row_to_predict)-1,column] = away_team_final_df[column].mean() + home_team_final_df[column].mean() / 2\n", "\n", "                    try:\n", "                        # Make the prediction\n", "                        X_test = row_to_predict.values\n", "                        prediction = league_model.predict(X_test)\n", "                        predicted_probability = league_model.predict_proba(X_test)[0]\n", "\n", "                        if prediction == 1:\n", "                            print(f\"The match between {home_team} and {away_team} will end with more than 2.5 goals. With a probability of {[round (prediction,2) for prediction in predicted_probability]}\")\n", "                            result = f\"Over 2.5 Goals! 🔥 ({round(predicted_probability[1] * 100, 2)}% chance)\"\n", "                        else:\n", "                            print(f\"The match between {home_team} and {away_team} will end with less than 2.5 goals. With a probability of {[round (prediction,2) for prediction in predicted_probability]}\")\n", "                            result = f\"Under 2.5 Goals ({round(predicted_probability[0] * 100, 2)}% chance)\"\n", "\n", "                        # Add the match result to the league section\n", "                        league_section += f\"- ⚽ **{home_team}** 🆚 **{away_team}**: {result}\\n\"\n", "                    except Exception as e:\n", "                        print(f\"An error occurred while making the prediction for the match between {home_team} and {away_team}\")\n", "                        print(f\"Error: {e}\")\n", "                        row_to_predict.head()\n", "                        continue\n", "\n", "        # Add the league section to the Telegram message\n", "        prediction_message += league_section + \"\\n\""]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🎯 **AI Football Predictions: Will There Be Over 2.5 Goals?** 🎯\n", "\n", "Check out the latest predictions for the upcoming football matches! We've analyzed the data and here are our thoughts:\n", " PREDICTIONS DONE: 2024-09-09 \n", "\n", "🔵 **Premier League**:\n", "- ⚽ **Southampton** 🆚 **Man United**: Under 2.5 Goals (61.63% chance)\n", "- ⚽ **Brighton** 🆚 **Ipswich**: Over 2.5 Goals! 🔥 (85.26% chance)\n", "- ⚽ **Crystal Palace** 🆚 **Leicester**: Over 2.5 Goals! 🔥 (71.3% chance)\n", "- ⚽ **Fulham** 🆚 **West Ham**: Over 2.5 Goals! 🔥 (75.39% chance)\n", "- ⚽ **Liverpool** 🆚 **Nott'm Forest**: Over 2.5 Goals! 🔥 (56.05% chance)\n", "- ⚽ **Man City** 🆚 **Brentford**: Over 2.5 Goals! 🔥 (86.87% chance)\n", "- ⚽ **Aston Villa** 🆚 **Everton**: Under 2.5 Goals (59.1% chance)\n", "- ⚽ **Bournemouth** 🆚 **Chelsea**: Over 2.5 Goals! 🔥 (89.64% chance)\n", "- ⚽ **Tottenham** 🆚 **Arsenal**: Over 2.5 Goals! 🔥 (84.23% chance)\n", "- ⚽ **Wolves** 🆚 **Newcastle**: Over 2.5 Goals! 🔥 (55.86% chance)\n", "\n", "🔵 **Serie A**:\n", "- ⚽ **Empoli** 🆚 **Juventus**: Under 2.5 Goals (74.29% chance)\n", "- ⚽ **Milan** 🆚 **Venezia**: Over 2.5 Goals! 🔥 (87.63% chance)\n", "- ⚽ **Genoa** 🆚 **Roma**: Under 2.5 Goals (52.86% chance)\n", "- ⚽ **Atalanta** 🆚 **Fiorentina**: Over 2.5 Goals! 🔥 (63.17% chance)\n", "- ⚽ **Torino** 🆚 **Lecce**: Under 2.5 Goals (81.17% chance)\n", "- ⚽ **Cagliari** 🆚 **Napoli**: Under 2.5 Goals (60.86% chance)\n", "- ⚽ **Monza** 🆚 **Inter**: Over 2.5 Goals! 🔥 (79.38% chance)\n", "- ⚽ **Parma** 🆚 **Udinese**: Under 2.5 Goals (88.69% chance)\n", "- ⚽ **Lazio** 🆚 **Verona**: Under 2.5 Goals (69.82% chance)\n", "\n", "🔵 **Bundesliga**:\n", "- ⚽ **Dortmund** 🆚 **Heidenheim**: Under 2.5 Goals (63.49% chance)\n", "- ⚽ **RB Leipzig** 🆚 **Union Berlin**: Under 2.5 Goals (93.12% chance)\n", "- ⚽ **Hoffenheim** 🆚 **Leverkusen**: Over 2.5 Goals! 🔥 (78.5% chance)\n", "- ⚽ **Freiburg** 🆚 **Bochum**: Over 2.5 Goals! 🔥 (56.02% chance)\n", "- ⚽ **Wolfsburg** 🆚 **Ein Frankfurt**: Over 2.5 Goals! 🔥 (62.83% chance)\n", "- ⚽ **<PERSON><PERSON><PERSON><PERSON>** 🆚 **Stuttgart**: Over 2.5 Goals! 🔥 (86.81% chance)\n", "- ⚽ **Holstein Kiel** 🆚 **Bayern Munich**: Over 2.5 Goals! 🔥 (75.41% chance)\n", "- ⚽ **Augsburg** 🆚 **St Pauli**: Under 2.5 Goals (89.11% chance)\n", "- ⚽ **Mainz** 🆚 **Werder Bremen**: Under 2.5 Goals (53.05% chance)\n", "\n", "🔵 **La Liga**:\n", "- ⚽ **Betis** 🆚 **<PERSON><PERSON><PERSON>**: Under 2.5 Goals (93.14% chance)\n", "- ⚽ **Mallorca** 🆚 **Villarreal**: Under 2.5 Goals (82.46% chance)\n", "- ⚽ **Espanol** 🆚 **Alaves**: Over 2.5 Goals! 🔥 (60.73% chance)\n", "- ⚽ **Sevilla** 🆚 **Getafe**: Over 2.5 Goals! 🔥 (57.53% chance)\n", "- ⚽ **Sociedad** 🆚 **Real Madrid**: Under 2.5 Goals (60.94% chance)\n", "- ⚽ **Celta** 🆚 **Valladolid**: Over 2.5 Goals! 🔥 (85.38% chance)\n", "- ⚽ **Girona** 🆚 **Barcelona**: Over 2.5 Goals! 🔥 (92.12% chance)\n", "- ⚽ **Las Palmas** 🆚 **Ath Bilbao**: Under 2.5 Goals (68.24% chance)\n", "- ⚽ **Ath Madrid** 🆚 **Valencia**: Over 2.5 Goals! 🔥 (73.62% chance)\n", "- ⚽ **Vallecano** 🆚 **<PERSON><PERSON><PERSON><PERSON>**: Over 2.5 Goals! 🔥 (81.71% chance)\n", "\n", "🔵 **Ligue 1**:\n", "- ⚽ **St Etienne** 🆚 **Lille**: Under 2.5 Goals (85.78% chance)\n", "- ⚽ **Marseille** 🆚 **Nice**: Over 2.5 Goals! 🔥 (82.33% chance)\n", "- ⚽ **Auxerre** 🆚 **Monaco**: Over 2.5 Goals! 🔥 (71.7% chance)\n", "- ⚽ **Paris SG** 🆚 **Brest**: Over 2.5 Goals! 🔥 (65.88% chance)\n", "- ⚽ **Rennes** 🆚 **Montpellier**: Over 2.5 Goals! 🔥 (80.37% chance)\n", "- ⚽ **Nantes** 🆚 **Reims**: Over 2.5 Goals! 🔥 (71.42% chance)\n", "- ⚽ **Toulouse** 🆚 **<PERSON> Havre**: Over 2.5 Goals! 🔥 (54.11% chance)\n", "- ⚽ **Strasbourg** 🆚 **Angers**: Over 2.5 Goals! 🔥 (75.02% chance)\n", "- ⚽ **Lens** 🆚 **Lyon**: Over 2.5 Goals! 🔥 (78.08% chance)\n", "\n", "\n"]}], "source": ["print(prediction_message)"]}, {"cell_type": "code", "execution_count": 151, "metadata": {}, "outputs": [], "source": ["# File path and name\n", "file_path = \"final_predictions.txt\"\n", "\n", "# Saving the string to a file\n", "with open(file_path, 'w', encoding='utf-8') as file:\n", "    file.write(prediction_message)"]}, {"cell_type": "code", "execution_count": 79, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Last5HomeOver2.5Perc</th>\n", "      <th>Last5AwayOver2.5Perc</th>\n", "      <th>HST</th>\n", "      <th>AST</th>\n", "      <th>HomeOver2.5Perc</th>\n", "      <th>AvgLast5AwayGoalsConceded</th>\n", "      <th>AwayOver2.5Perc</th>\n", "      <th>AvgLast5HomeGoalsScored</th>\n", "      <th>AvgLast5HomeGoalsConceded</th>\n", "      <th>AvgLast5AwayGoalsScored</th>\n", "      <th>MaxC&gt;2.5</th>\n", "      <th>B365C&lt;2.5</th>\n", "      <th>AvgHomeGoalsScored</th>\n", "      <th>HR</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>365</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>5</td>\n", "      <td>2</td>\n", "      <td>0.00</td>\n", "      <td>1.0</td>\n", "      <td>0.00</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.66</td>\n", "      <td>2.30</td>\n", "      <td>1.00</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>575</th>\n", "      <td>60.0</td>\n", "      <td>60.0</td>\n", "      <td>8</td>\n", "      <td>8</td>\n", "      <td>68.42</td>\n", "      <td>1.8</td>\n", "      <td>73.68</td>\n", "      <td>2.0</td>\n", "      <td>1.6</td>\n", "      <td>1.8</td>\n", "      <td>1.40</td>\n", "      <td>3.20</td>\n", "      <td>1.63</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>40.0</td>\n", "      <td>40.0</td>\n", "      <td>2</td>\n", "      <td>5</td>\n", "      <td>68.42</td>\n", "      <td>0.4</td>\n", "      <td>52.63</td>\n", "      <td>1.8</td>\n", "      <td>1.2</td>\n", "      <td>1.8</td>\n", "      <td>1.40</td>\n", "      <td>3.00</td>\n", "      <td>1.63</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>210</th>\n", "      <td>60.0</td>\n", "      <td>60.0</td>\n", "      <td>10</td>\n", "      <td>7</td>\n", "      <td>68.42</td>\n", "      <td>1.4</td>\n", "      <td>63.16</td>\n", "      <td>2.0</td>\n", "      <td>1.4</td>\n", "      <td>1.8</td>\n", "      <td>1.41</td>\n", "      <td>3.75</td>\n", "      <td>1.63</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>631</th>\n", "      <td>80.0</td>\n", "      <td>60.0</td>\n", "      <td>13</td>\n", "      <td>4</td>\n", "      <td>68.42</td>\n", "      <td>2.4</td>\n", "      <td>57.89</td>\n", "      <td>2.4</td>\n", "      <td>1.2</td>\n", "      <td>1.0</td>\n", "      <td>1.37</td>\n", "      <td>3.40</td>\n", "      <td>1.63</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     Last5HomeOver2.5Perc  Last5AwayOver2.5Perc  HST  AST  HomeOver2.5Perc  \\\n", "365                   0.0                   0.0    5    2             0.00   \n", "575                  60.0                  60.0    8    8            68.42   \n", "37                   40.0                  40.0    2    5            68.42   \n", "210                  60.0                  60.0   10    7            68.42   \n", "631                  80.0                  60.0   13    4            68.42   \n", "\n", "     AvgLast5AwayGoalsConceded  AwayOver2.5Perc  AvgLast5HomeGoalsScored  \\\n", "365                        1.0             0.00                      1.0   \n", "575                        1.8            73.68                      2.0   \n", "37                         0.4            52.63                      1.8   \n", "210                        1.4            63.16                      2.0   \n", "631                        2.4            57.89                      2.4   \n", "\n", "     AvgLast5HomeGoalsConceded  AvgLast5AwayGoalsScored  MaxC>2.5  B365C<2.5  \\\n", "365                        0.0                      0.0      1.66       2.30   \n", "575                        1.6                      1.8      1.40       3.20   \n", "37                         1.2                      1.8      1.40       3.00   \n", "210                        1.4                      1.8      1.41       3.75   \n", "631                        1.2                      1.0      1.37       3.40   \n", "\n", "     AvgHomeGoalsScored  HR  \n", "365                1.00   0  \n", "575                1.63   0  \n", "37                 1.63   0  \n", "210                1.63   0  \n", "631                1.63   0  "]}, "execution_count": 79, "metadata": {}, "output_type": "execute_result"}], "source": ["home_team_final_df.head()"]}, {"cell_type": "code", "execution_count": 80, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Last5HomeOver2.5Perc</th>\n", "      <th>Last5AwayOver2.5Perc</th>\n", "      <th>HST</th>\n", "      <th>AST</th>\n", "      <th>HomeOver2.5Perc</th>\n", "      <th>AvgLast5AwayGoalsConceded</th>\n", "      <th>AwayOver2.5Perc</th>\n", "      <th>AvgLast5HomeGoalsScored</th>\n", "      <th>AvgLast5HomeGoalsConceded</th>\n", "      <th>AvgLast5AwayGoalsScored</th>\n", "      <th>MaxC&gt;2.5</th>\n", "      <th>B365C&lt;2.5</th>\n", "      <th>AvgHomeGoalsScored</th>\n", "      <th>HR</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>442</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>2</td>\n", "      <td>5</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "      <td>2.0</td>\n", "      <td>2.0</td>\n", "      <td>1.40</td>\n", "      <td>3.20</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>441</th>\n", "      <td>80.0</td>\n", "      <td>80.0</td>\n", "      <td>5</td>\n", "      <td>7</td>\n", "      <td>78.95</td>\n", "      <td>2.0</td>\n", "      <td>63.16</td>\n", "      <td>2.6</td>\n", "      <td>1.8</td>\n", "      <td>2.0</td>\n", "      <td>1.39</td>\n", "      <td>3.20</td>\n", "      <td>2.53</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>440</th>\n", "      <td>40.0</td>\n", "      <td>60.0</td>\n", "      <td>8</td>\n", "      <td>8</td>\n", "      <td>52.63</td>\n", "      <td>1.4</td>\n", "      <td>63.16</td>\n", "      <td>1.2</td>\n", "      <td>1.6</td>\n", "      <td>1.6</td>\n", "      <td>1.42</td>\n", "      <td>3.20</td>\n", "      <td>1.63</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>439</th>\n", "      <td>20.0</td>\n", "      <td>60.0</td>\n", "      <td>6</td>\n", "      <td>7</td>\n", "      <td>36.84</td>\n", "      <td>1.2</td>\n", "      <td>63.16</td>\n", "      <td>1.4</td>\n", "      <td>0.8</td>\n", "      <td>2.0</td>\n", "      <td>1.53</td>\n", "      <td>2.63</td>\n", "      <td>1.16</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>438</th>\n", "      <td>80.0</td>\n", "      <td>80.0</td>\n", "      <td>5</td>\n", "      <td>7</td>\n", "      <td>63.16</td>\n", "      <td>1.4</td>\n", "      <td>63.16</td>\n", "      <td>1.6</td>\n", "      <td>1.2</td>\n", "      <td>2.2</td>\n", "      <td>1.48</td>\n", "      <td>2.75</td>\n", "      <td>1.63</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     Last5HomeOver2.5Perc  Last5AwayOver2.5Perc  HST  AST  HomeOver2.5Perc  \\\n", "442                   0.0                   0.0    2    5             0.00   \n", "441                  80.0                  80.0    5    7            78.95   \n", "440                  40.0                  60.0    8    8            52.63   \n", "439                  20.0                  60.0    6    7            36.84   \n", "438                  80.0                  80.0    5    7            63.16   \n", "\n", "     AvgLast5AwayGoalsConceded  AwayOver2.5Perc  AvgLast5HomeGoalsScored  \\\n", "442                        0.0             0.00                      0.0   \n", "441                        2.0            63.16                      2.6   \n", "440                        1.4            63.16                      1.2   \n", "439                        1.2            63.16                      1.4   \n", "438                        1.4            63.16                      1.6   \n", "\n", "     AvgLast5HomeGoalsConceded  AvgLast5AwayGoalsScored  MaxC>2.5  B365C<2.5  \\\n", "442                        2.0                      2.0      1.40       3.20   \n", "441                        1.8                      2.0      1.39       3.20   \n", "440                        1.6                      1.6      1.42       3.20   \n", "439                        0.8                      2.0      1.53       2.63   \n", "438                        1.2                      2.2      1.48       2.75   \n", "\n", "     AvgHomeGoalsScored  HR  \n", "442                0.00   0  \n", "441                2.53   0  \n", "440                1.63   0  \n", "439                1.16   0  \n", "438                1.63   0  "]}, "execution_count": 80, "metadata": {}, "output_type": "execute_result"}], "source": ["away_team_final_df.head()"]}, {"cell_type": "code", "execution_count": 81, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Last5HomeOver2.5Perc</th>\n", "      <th>Last5AwayOver2.5Perc</th>\n", "      <th>HST</th>\n", "      <th>AST</th>\n", "      <th>HomeOver2.5Perc</th>\n", "      <th>AvgLast5AwayGoalsConceded</th>\n", "      <th>AwayOver2.5Perc</th>\n", "      <th>AvgLast5HomeGoalsScored</th>\n", "      <th>AvgLast5HomeGoalsConceded</th>\n", "      <th>AvgLast5AwayGoalsScored</th>\n", "      <th>MaxC&gt;2.5</th>\n", "      <th>B365C&lt;2.5</th>\n", "      <th>AvgHomeGoalsScored</th>\n", "      <th>HR</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>48.0</td>\n", "      <td>56.0</td>\n", "      <td>7.6</td>\n", "      <td>6.8</td>\n", "      <td>54.736</td>\n", "      <td>1.2</td>\n", "      <td>50.528</td>\n", "      <td>1.84</td>\n", "      <td>1.08</td>\n", "      <td>1.96</td>\n", "      <td>2.168</td>\n", "      <td>4.561</td>\n", "      <td>1.504</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  Last5HomeOver2.5Perc Last5AwayOver2.5Perc  HST  AST HomeOver2.5Perc  \\\n", "0                 48.0                 56.0  7.6  6.8          54.736   \n", "\n", "  AvgLast5AwayGoalsConceded AwayOver2.5Perc AvgLast5HomeGoalsScored  \\\n", "0                       1.2          50.528                    1.84   \n", "\n", "  AvgLast5HomeGoalsConceded AvgLast5AwayGoalsScored MaxC>2.5 B365C<2.5  \\\n", "0                      1.08                    1.96    2.168     4.561   \n", "\n", "  AvgHomeGoalsScored   HR  \n", "0              1.504  0.0  "]}, "execution_count": 81, "metadata": {}, "output_type": "execute_result"}], "source": ["row_to_predict.head()"]}, {"cell_type": "code", "execution_count": 93, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[48.0, 56.0, 7.6, 6.8, 54.736000000000004, 1.2, 50.528,\n", "        1.8399999999999999, 1.0799999999999998, 1.9600000000000002,\n", "        2.168, 4.561, 1.504, 0.0]], dtype=object)"]}, "execution_count": 93, "metadata": {}, "output_type": "execute_result"}], "source": ["X_test = row_to_predict.values\n", "X_test"]}, {"cell_type": "code", "execution_count": 95, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Prediction: [1]\n"]}], "source": ["prediction = models['uk_model'].predict(X_test)\n", "print(f\"Prediction: {prediction}\")"]}, {"cell_type": "code", "execution_count": 98, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Prediction probabilities of Over2.5: [0.45, 0.55]\n"]}], "source": ["# get the probabilities\n", "prediction = models['uk_model'].predict_proba(X_test)[0]\n", "print(f\"Prediction probabilities of Over2.5: {[round (prediction,2) for prediction in prediction]}\")"]}], "metadata": {"kernelspec": {"display_name": "aifootball_predictions", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 2}