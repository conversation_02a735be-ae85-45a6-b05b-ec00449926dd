# 🚀 نظام توقعات كرة القدم - لوحة التحكم الويب المتقدمة

## 📋 نظرة عامة

تم تطوير لوحة تحكم ويب متقدمة وشاملة لنظام توقعات كرة القدم بالذكاء الاصطناعي. هذه اللوحة تتيح لك إدارة النظام بالكامل من خلال واجهة ويب سهلة الاستخدام.

## ✨ الميزات الجديدة

### 🎛️ لوحة التحكم الرئيسية
- **تحديث المباريات**: سحب المباريات القادمة من APIs
- **عمل التنبؤات**: تشغيل نماذج الذكاء الاصطناعي
- **إعادة تدريب النماذج**: تحديث النماذج بأحدث البيانات
- **تحميل البيانات**: جمع البيانات التاريخية

### 📊 الإحصائيات المباشرة
- عدد المباريات اليوم
- عدد الدوريات المدعومة
- معدل دقة النماذج
- وقت آخر تحديث

### 🎯 عرض التنبؤات المتقدم
- تصنيف حسب الدوريات (أوروبية كبرى، عربية، أخرى)
- فلترة حسب مستوى الثقة
- فلترة حسب نوع التنبؤ (Over/Under 2.5)
- عرض نسبة الثقة لكل تنبؤ

### 📝 سجل العمليات
- مراقبة العمليات الجارية في الوقت الفعلي
- سجل مفصل لجميع العمليات
- إمكانية تحميل السجلات

### ⚙️ الإعدادات المتقدمة
- إدارة مفاتيح API
- اختيار الدوريات المطلوبة
- ضبط معايير النماذج
- التحديث التلقائي

### 📈 التحليل والرسوم البيانية
- رسوم بيانية لتوزيع التنبؤات
- تحليل دقة النماذج حسب الدوري
- تطور الأداء عبر الوقت

## 🗂️ الملفات الجديدة

### واجهة الويب
```
web/
├── dashboard.html          # لوحة التحكم الرئيسية
├── dashboard.js           # منطق JavaScript
├── index.html            # الواجهة الأصلية (محدثة)
└── predictions.json      # ملف التنبؤات
```

### الخادم الخلفي
```
scripts/
├── web_server.py         # خادم Flask للواجهة
├── daily_web_update.py   # التحديث اليومي المتقدم
└── server.py            # الخادم الأصلي (محدث)
```

### ملفات التشغيل
```
├── start_web_dashboard.bat    # تشغيل لوحة التحكم
├── daily_web_update.bat      # التحديث اليومي
└── requirements.txt          # المكتبات المحدثة
```

## 🚀 طريقة التشغيل

### 1. تشغيل لوحة التحكم الويب
```bash
# الطريقة السهلة
start_web_dashboard.bat

# أو يدوياً
python scripts/web_server.py
```

### 2. الوصول للواجهة
- **لوحة التحكم**: http://localhost:5000/dashboard.html
- **الواجهة الأصلية**: http://localhost:5000/index.html
- **API**: http://localhost:5000/api/

### 3. التحديث اليومي
```bash
# تحديث شامل
daily_web_update.bat

# أو يدوياً
python scripts/daily_web_update.py
```

## 🎮 كيفية الاستخدام

### الخطوة 1: تشغيل النظام
1. شغل `start_web_dashboard.bat`
2. انتظر حتى يبدأ الخادم
3. افتح المتصفح على http://localhost:5000

### الخطوة 2: تحديث المباريات
1. اضغط على زر "تحديث المباريات"
2. راقب التقدم في شريط التقدم
3. تابع السجل في تبويب "سجل العمليات"

### الخطوة 3: عمل التنبؤات
1. اضغط على زر "عمل التنبؤات"
2. انتظر حتى تكتمل العملية
3. شاهد النتائج في تبويب "التنبؤات"

### الخطوة 4: مراجعة النتائج
1. استخدم الفلاتر لتصفية التنبؤات
2. راجع الإحصائيات في الرسوم البيانية
3. صدر النتائج إذا لزم الأمر

## 🔧 الإعدادات المتقدمة

### إعدادات API
- **مفتاح API Football**: أدخل مفتاحك الخاص
- **فترة التحديث**: اضبط التحديث التلقائي

### إعدادات النماذج
- **حد الدقة**: الحد الأدنى لدقة النموذج
- **حد الثقة**: الحد الأدنى لثقة التنبؤ
- **التدريب التلقائي**: إعادة تدريب أسبوعية

### اختيار الدوريات
- اختر الدوريات التي تريد متابعتها
- يمكن اختيار دوريات متعددة
- التحديث الفوري للاختيارات

## 📊 API المتاح

### نقاط النهاية الرئيسية
```
GET  /api/predictions      # الحصول على التنبؤات
GET  /api/stats           # إحصائيات النظام
POST /api/update-matches  # تحديث المباريات
POST /api/make-predictions # عمل التنبؤات
POST /api/retrain-models  # إعادة تدريب النماذج
POST /api/download-data   # تحميل البيانات
GET  /api/logs           # سجل العمليات
```

### مثال على الاستخدام
```javascript
// الحصول على التنبؤات
fetch('/api/predictions')
  .then(response => response.json())
  .then(data => console.log(data));

// تحديث المباريات
fetch('/api/update-matches', { method: 'POST' })
  .then(response => response.json())
  .then(data => console.log(data));
```

## 🔄 التحديث التلقائي

### التحديث اليومي
- يتم تشغيله تلقائياً كل يوم
- ينظف الملفات القديمة
- يحدث المباريات
- يولد التنبؤات الجديدة

### مراقبة العمليات
- مراقبة مباشرة للعمليات الجارية
- سجل مفصل لكل خطوة
- تنبيهات في حالة الأخطاء

## 🛠️ استكشاف الأخطاء

### مشاكل شائعة
1. **الخادم لا يبدأ**: تأكد من تثبيت Flask
2. **لا توجد تنبؤات**: شغل تحديث المباريات أولاً
3. **أخطاء API**: تحقق من مفتاح API

### ملفات السجل
- `logs/daily_update.log`: سجل التحديث اليومي
- سجل الخادم في الكونسول
- سجل العمليات في الواجهة

## 🎯 المميزات المستقبلية

- [ ] تنبيهات الهاتف المحمول
- [ ] تصدير التقارير PDF
- [ ] تحليل الأداء التاريخي
- [ ] دعم المزيد من أنواع الرهانات
- [ ] واجهة الهاتف المحمول

## 📞 الدعم

إذا واجهت أي مشاكل:
1. تحقق من ملفات السجل
2. راجع الإعدادات
3. أعد تشغيل النظام
4. تأكد من اتصال الإنترنت

---

**🎉 استمتع بتجربة توقعات كرة القدم المتقدمة!**
