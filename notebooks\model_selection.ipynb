{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["### Model Selection\n", "\n", "### Overview\n", "\n", "This notebook focuses on training a `VotingClassifier`, an ensemble machine learning model that combines predictions from multiple individual classifiers to improve overall performance. The data used for training both the individual classifiers and the ensemble model is sourced from the `data/processed` directory. The main steps involved in this notebook are:\n", "\n", "- **Model Selection**: Choose multiple classifiers (e.g., KNN, Logistic Regression, SVM, XGBoost, etc.) to be part of the ensemble.\n", "- **Hyperparameter Tuning**: Perform hyperparameter optimization for each classifier to identify the best configuration for the given dataset.\n", "- **Ensemble Creation**: Combine the tuned classifiers into a `VotingClassifier` ensemble, which aggregates the predictions of the individual models through majority voting (or soft voting based on predicted probabilities).\n", "- **Model Training**: Train the `VotingClassifier` on the processed data and evaluate its performance.\n", "\n", "### Pre-requisites \n", "\n", "To run this notebook, you need to set up a conda environment with all required dependencies.\n", "\n", "Example setup:\n", "```bash\n", "cd path/to/conda/dir\n", "conda env create -f aifootball_predictions.yaml\n", "conda activate aifootball_predictions\n", "python -m ipykernel install --user --name aifootball_predictions --display-name \"aifootball_predictions\"\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Import the necessary libraries\n", "import numpy as np\n", "import pandas as pd\n", "import os\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.experimental import enable_halving_search_cv # noqa\n", "from sklearn.model_selection import GridSearchCV, cross_val_score, KFold, HalvingGridSearchCV\n", "from sklearn.metrics import make_scorer, accuracy_score, precision_score, f1_score, roc_auc_score\n", "from sklearn.pipeline import Pipeline\n", "import xgboost as xgb\n", "import numpy as np\n", "import tensorflow as tf\n", "from tensorflow.python.client import device_lib\n", "from tensorflow.keras.models import Sequential\n", "from tensorflow.keras.layers import LSTM, Dense, Dropout, BatchNormalization\n", "from tensorflow.keras.optimizers import Adam\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.neighbors import KNeighborsClassifier\n", "from sklearn.svm import SVC\n", "from sklearn.ensemble import RandomForestClassifier, HistGradientBoostingClassifier, VotingClassifier\n", "from skopt import BayesSearchCV\n", "from skopt.space import Real, Integer, Categorical"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### GPU checks"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[name: \"/device:CPU:0\"\n", "device_type: \"CPU\"\n", "memory_limit: 268435456\n", "locality {\n", "}\n", "incarnation: 905076402564013371\n", "xla_global_id: -1\n", ", name: \"/device:GPU:0\"\n", "device_type: \"GPU\"\n", "memory_limit: 1734606848\n", "locality {\n", "  bus_id: 1\n", "  links {\n", "  }\n", "}\n", "incarnation: 11367158361600899005\n", "physical_device_desc: \"device: 0, name: NVIDIA GeForce RTX 3050 Laptop GPU, pci bus id: 0000:01:00.0, compute capability: 8.6\"\n", "xla_global_id: 416903419\n", "]\n"]}], "source": ["# Check the available devices\n", "print(device_lib.list_local_devices())"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["# Check if TensorFlow is built with CUDA support\n", "tf.test.is_built_with_cuda()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Num GPUs Available:  1\n"]}], "source": ["# Check the available GPUs\n", "print(\"Num GPUs Available: \", len(tf.config.list_physical_devices('GPU')))"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[PhysicalDevice(name='/physical_device:GPU:0', device_type='GPU')]\n"]}], "source": ["print(tf.config.list_physical_devices('GPU'))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Model Selection"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# read the data\n", "uk_data = pd.read_csv('../data/processed/E0_merged_preprocessed.csv')"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Date</th>\n", "      <th>Div</th>\n", "      <th>Time</th>\n", "      <th>HomeTeam</th>\n", "      <th>AwayTeam</th>\n", "      <th>FTR</th>\n", "      <th>HTR</th>\n", "      <th>Referee</th>\n", "      <th>Season</th>\n", "      <th>Last5HomeOver2.5Perc</th>\n", "      <th>...</th>\n", "      <th>HomeOver2.5Perc</th>\n", "      <th>AvgLast5AwayGoalsConceded</th>\n", "      <th>AvgLast5HomeGoalsScored</th>\n", "      <th>AwayOver2.5Perc</th>\n", "      <th>AvgLast5HomeGoalsConceded</th>\n", "      <th>AvgLast5AwayGoalsScored</th>\n", "      <th>B365C&lt;2.5</th>\n", "      <th>MaxC&gt;2.5</th>\n", "      <th>HR</th>\n", "      <th>Over2.5</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2022-08-05</td>\n", "      <td>E0</td>\n", "      <td>20:00</td>\n", "      <td>Crystal Palace</td>\n", "      <td>Arsenal</td>\n", "      <td>A</td>\n", "      <td>A</td>\n", "      <td>A <PERSON></td>\n", "      <td>2022/2023</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>42.11</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "      <td>47.37</td>\n", "      <td>2.00</td>\n", "      <td>2.00</td>\n", "      <td>1.72</td>\n", "      <td>2.19</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2022-08-20</td>\n", "      <td>E0</td>\n", "      <td>17:30</td>\n", "      <td>Bournemouth</td>\n", "      <td>Arsenal</td>\n", "      <td>A</td>\n", "      <td>A</td>\n", "      <td><PERSON></td>\n", "      <td>2022/2023</td>\n", "      <td>50.0</td>\n", "      <td>...</td>\n", "      <td>47.37</td>\n", "      <td>0.00</td>\n", "      <td>1.0</td>\n", "      <td>47.37</td>\n", "      <td>1.50</td>\n", "      <td>2.50</td>\n", "      <td>2.10</td>\n", "      <td>1.90</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2022-09-04</td>\n", "      <td>E0</td>\n", "      <td>16:30</td>\n", "      <td>Man United</td>\n", "      <td>Arsenal</td>\n", "      <td>H</td>\n", "      <td>H</td>\n", "      <td><PERSON></td>\n", "      <td>2022/2023</td>\n", "      <td>100.0</td>\n", "      <td>...</td>\n", "      <td>57.89</td>\n", "      <td>1.00</td>\n", "      <td>2.0</td>\n", "      <td>47.37</td>\n", "      <td>1.33</td>\n", "      <td>2.00</td>\n", "      <td>2.10</td>\n", "      <td>1.82</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2022-09-18</td>\n", "      <td>E0</td>\n", "      <td>12:00</td>\n", "      <td>Brentford</td>\n", "      <td>Arsenal</td>\n", "      <td>A</td>\n", "      <td>A</td>\n", "      <td><PERSON></td>\n", "      <td>2022/2023</td>\n", "      <td>75.0</td>\n", "      <td>...</td>\n", "      <td>47.37</td>\n", "      <td>0.75</td>\n", "      <td>2.5</td>\n", "      <td>47.37</td>\n", "      <td>1.50</td>\n", "      <td>2.25</td>\n", "      <td>2.10</td>\n", "      <td>1.81</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2022-10-16</td>\n", "      <td>E0</td>\n", "      <td>14:00</td>\n", "      <td>Leeds</td>\n", "      <td>Arsenal</td>\n", "      <td>A</td>\n", "      <td>A</td>\n", "      <td><PERSON></td>\n", "      <td>2022/2023</td>\n", "      <td>40.0</td>\n", "      <td>...</td>\n", "      <td>63.16</td>\n", "      <td>0.60</td>\n", "      <td>1.2</td>\n", "      <td>47.37</td>\n", "      <td>0.60</td>\n", "      <td>2.00</td>\n", "      <td>2.30</td>\n", "      <td>1.64</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 23 columns</p>\n", "</div>"], "text/plain": ["         Date Div   Time        HomeTeam AwayTeam FTR HTR     Referee  \\\n", "0  2022-08-05  E0  20:00  Crystal Palace  Arsenal   A   A    A Taylor   \n", "1  2022-08-20  E0  17:30     Bournemouth  Arsenal   A   A    C Pawson   \n", "2  2022-09-04  E0  16:30      Man United  Arsenal   H   H   P Tierney   \n", "3  2022-09-18  E0  12:00       Brentford  Arsenal   A   A     D Coote   \n", "4  2022-10-16  E0  14:00           Leeds  Arsenal   A   A  C Kavanagh   \n", "\n", "      Season  Last5HomeOver2.5Perc  ...  HomeOver2.5Perc  \\\n", "0  2022/2023                   0.0  ...            42.11   \n", "1  2022/2023                  50.0  ...            47.37   \n", "2  2022/2023                 100.0  ...            57.89   \n", "3  2022/2023                  75.0  ...            47.37   \n", "4  2022/2023                  40.0  ...            63.16   \n", "\n", "   AvgLast5AwayGoalsConceded  AvgLast5HomeGoalsScored  AwayOver2.5Perc  \\\n", "0                       0.00                      0.0            47.37   \n", "1                       0.00                      1.0            47.37   \n", "2                       1.00                      2.0            47.37   \n", "3                       0.75                      2.5            47.37   \n", "4                       0.60                      1.2            47.37   \n", "\n", "   AvgLast5HomeGoalsConceded  AvgLast5AwayGoalsScored  B365C<2.5  MaxC>2.5  \\\n", "0                       2.00                     2.00       1.72      2.19   \n", "1                       1.50                     2.50       2.10      1.90   \n", "2                       1.33                     2.00       2.10      1.82   \n", "3                       1.50                     2.25       2.10      1.81   \n", "4                       0.60                     2.00       2.30      1.64   \n", "\n", "   HR  Over2.5  \n", "0   0        0  \n", "1   0        1  \n", "2   0        1  \n", "3   0        1  \n", "4   0        0  \n", "\n", "[5 rows x 23 columns]"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["# show the first 5 rows of the data\n", "uk_data.head()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# select the target variable\n", "y = uk_data['Over2.5'].values\n", "\n", "# Select only numerical columns for X, excluding 'Date' and the target variable 'Over2.5'\n", "numerical_columns = uk_data.select_dtypes(include=['number']).columns\n", "X = uk_data[numerical_columns].drop(columns=['Over2.5']).values"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['Last5HomeOver2.5Perc', 'Last5AwayOver2.5Perc', 'HST', 'AST',\n", "       'HomeOver2.5Perc', 'AvgLast5AwayGoalsConceded',\n", "       'AvgLast5HomeGoalsScored', 'AwayOver2.5Perc',\n", "       'AvgLast5HomeGoalsConceded', 'AvgLast5AwayGoalsScored', 'B365C<2.5',\n", "       'MaxC>2.5', 'HR', 'Over2.5'],\n", "      dtype='object')"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["numerical_columns"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([0, 1, 1, 1, 0, 0, 0, 0, 1, 0, 0, 1, 0, 1, 1, 1, 1, 0, 0, 0, 0, 1,\n", "       1, 0, 0, 1, 0, 0, 1, 1, 1, 1, 1, 0, 1, 0, 1, 0, 0, 1, 1, 0, 0, 1,\n", "       1, 1, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 1, 1, 1,\n", "       1, 0, 1, 1, 1, 0, 1, 0, 0, 1, 1, 1, 1, 0, 1, 0, 1, 0, 1, 0, 0, 0,\n", "       1, 1, 0, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 0, 1, 1, 0, 1,\n", "       1, 0, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0,\n", "       1, 1, 0, 0, 1, 0, 1, 1, 0, 1, 1, 0, 0, 1, 1, 1, 1, 1, 0, 1, 1, 0,\n", "       1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0,\n", "       1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 1, 0, 0, 0, 1, 1, 1, 1, 0, 0,\n", "       0, 1, 1, 1, 1, 1, 1, 0, 1, 0, 1, 0, 1, 1, 1, 0, 0, 1, 0, 0, 1, 0,\n", "       0, 0, 1, 0, 1, 1, 0, 1, 1, 0, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 1,\n", "       1, 1, 1, 1, 1, 0, 1, 0, 0, 1, 1, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, 0,\n", "       0, 1, 0, 0, 1, 0, 1, 0, 1, 0, 1, 1, 1, 1, 0, 1, 0, 0, 0, 0, 1, 1,\n", "       0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 1, 1, 1, 0, 0, 1, 1, 0, 1, 1, 1, 0,\n", "       0, 1, 0, 0, 1, 1, 0, 0, 0, 0, 1, 0, 1, 0, 1, 0, 1, 1, 1, 1, 1, 1,\n", "       1, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 1, 0, 1, 1, 0, 0, 0, 1, 1, 1, 1,\n", "       0, 1, 1, 1, 1, 1, 0, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 0, 0,\n", "       1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 0, 1, 1, 0, 0, 1, 1,\n", "       0, 1, 0, 1, 1, 0, 1, 0, 1, 1, 1, 1, 1, 0, 0, 0, 1, 0, 1, 1, 1, 1,\n", "       0, 1, 1, 1, 1, 0, 0, 0, 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 0,\n", "       1, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 1, 0, 1, 0, 1, 0, 0,\n", "       1, 0, 1, 0, 1, 0, 1, 0, 1, 1, 1, 0, 0, 1, 1, 1, 1, 0, 1, 1, 0, 1,\n", "       1, 1, 1, 0, 0, 1, 1, 0, 1, 0, 1, 0, 0, 1, 1, 0, 1, 1, 0, 0, 1, 0,\n", "       1, 0, 0, 1, 0, 0, 0, 0, 1, 0, 1, 0, 1, 0, 0, 0, 1, 1, 1, 1, 1, 0,\n", "       1, 1, 1, 0, 0, 0, 1, 1, 0, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1,\n", "       0, 0, 1, 1, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 0, 0,\n", "       1, 1, 0, 0, 1, 1, 0, 0, 0, 1, 1, 1, 0, 1, 1, 1, 0, 1, 1, 0, 0, 0,\n", "       1, 1, 1, 0, 1, 1, 0, 1, 0, 0, 1, 0, 1, 1, 1, 1, 0, 1, 1, 0, 1, 0,\n", "       0, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 0, 1, 0, 0, 1, 1, 1, 1,\n", "       0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 1, 0, 0, 1, 1, 1, 1, 0, 1, 0, 1,\n", "       0, 1, 1, 1, 1, 1, 0, 1, 1, 0, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 0,\n", "       1, 1, 0, 0, 1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 1, 0, 1, 1, 1, 0, 1, 0,\n", "       1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0,\n", "       1, 1, 0, 1, 0, 1, 1, 0, 0, 1, 0, 1, 1, 0, 1, 0, 0, 1, 0, 1, 1, 1,\n", "       1, 1, 1, 0, 1, 1, 1, 0, 0, 1, 1, 0], dtype=int64)"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["y"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[  0.  ,   0.  ,   2.  , ...,   1.72,   2.19,   0.  ],\n", "       [ 50.  ,  50.  ,   1.  , ...,   2.1 ,   1.9 ,   0.  ],\n", "       [100.  ,  66.67,   6.  , ...,   2.1 ,   1.82,   0.  ],\n", "       ...,\n", "       [ 40.  ,  60.  ,   9.  , ...,   1.95,   2.  ,   0.  ],\n", "       [ 80.  ,  60.  ,  12.  , ...,   4.  ,   1.28,   0.  ],\n", "       [ 60.  ,  40.  ,  14.  , ...,   5.  ,   1.19,   0.  ]])"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["X"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Nested Cross Validation"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["def create_dnn_model(input_dim: int, dropout_rate: float = 0.5) -> tf.keras.Model:\n", "    \"\"\"\n", "    Creates a Deep Neural Network (DNN) model for binary classification.\n", "\n", "    Parameters:\n", "    ----------\n", "    input_dim : int\n", "        The number of input features (dimensions).\n", "    dropout_rate : float, optional\n", "        The dropout rate to be used in Dropout layers to prevent overfitting (default is 0.5).\n", "\n", "    Returns:\n", "    -------\n", "    tf.keras.Model\n", "        A compiled DNN model ready for training.\n", "    \"\"\"\n", "    model = Sequential()\n", "\n", "    # Input layer\n", "    model.add(Den<PERSON>(128, activation='relu', input_dim=input_dim))\n", "    model.add(BatchNormalization())\n", "    model.add(Dropout(dropout_rate))\n", "\n", "    # Hidden layers\n", "    model.add(<PERSON><PERSON>(64, activation='relu'))\n", "    model.add(BatchNormalization())\n", "    model.add(Dropout(dropout_rate))\n", "\n", "    model.add(<PERSON><PERSON>(32, activation='relu'))\n", "    model.add(BatchNormalization())\n", "    model.add(Dropout(dropout_rate))\n", "\n", "    # Output layer\n", "    model.add(Dense(1, activation='sigmoid'))\n", "\n", "    # Compile the model\n", "    model.compile(optimizer=<PERSON>(learning_rate=0.001),\n", "                  loss='binary_crossentropy',\n", "                  metrics=['accuracy'])\n", "\n", "    return model\n"]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [], "source": ["def create_lstm_model(input_shape: tuple, dropout_rate: float = 0.5) -> tf.keras.Model:\n", "    \"\"\"\n", "    Creates an LSTM model for binary classification.\n", "\n", "    Parameters:\n", "    ----------\n", "    input_shape : tuple\n", "        The shape of the input data (timesteps, features).\n", "    dropout_rate : float, optional\n", "        The dropout rate to be used in Dropout layers to prevent overfitting (default is 0.5).\n", "\n", "    Returns:\n", "    -------\n", "    tf.keras.Model\n", "        A compiled LSTM model ready for training.\n", "    \"\"\"\n", "    model = Sequential()\n", "\n", "    # LSTM layer\n", "    model.add(LSTM(128, activation='relu', input_shape=input_shape))\n", "    model.add(BatchNormalization())\n", "    model.add(Dropout(dropout_rate))\n", "\n", "    model.add(LSTM(64, activation='relu', input_shape=input_shape))\n", "    model.add(BatchNormalization())\n", "    model.add(Dropout(dropout_rate))\n", "\n", "    # Dense hidden layer\n", "    model.add(<PERSON><PERSON>(32, activation='relu'))\n", "    model.add(BatchNormalization())\n", "    model.add(Dropout(dropout_rate))\n", "\n", "    # Output layer\n", "    model.add(Dense(1, activation='sigmoid'))\n", "\n", "    # Compile the model\n", "    model.compile(optimizer=<PERSON>(learning_rate=0.001),\n", "                  loss='binary_crossentropy',\n", "                  metrics=['accuracy'], verbose=1)\n", "\n", "    return model\n"]}, {"cell_type": "code", "execution_count": 68, "metadata": {}, "outputs": [], "source": ["# Logistic Regression Model and Hyperparameters\n", "lr_model = LogisticRegression(solver='liblinear')\n", "lr_param_grid = {\n", "    'C': [0.01, 0.1, 1, 10],\n", "    'penalty': ['l1', 'l2'],\n", "    'max_iter': [2000, 3000]  # Add max_iter as a hyperparameter to tune\n", "}\n", "\n", "# K-Nearest Neighbors Model and Hyperparameters\n", "knn_model = KNeighborsClassifier()\n", "knn_param_grid = {\n", "    'n_neighbors': [3, 5, 7, 9],\n", "    'weights': ['uniform', 'distance'],\n", "    'metric': ['euclidean', 'manhattan']\n", "}\n", "\n", "# Support Vector Machine Model and Hyperparameters\n", "svm_model = SVC(probability=True)\n", "svm_param_grid = {\n", "    'C': [0.1, 1, 10],\n", "    'kernel': ['linear', 'rbf', 'poly'],\n", "    'gamma': ['scale', 'auto'],\n", "    'degree': [2, 3, 4, 5],\n", "    'class_weight': [None, 'balanced']\n", "\n", "}\n", "\n", "# Random Forest Model and Hyperparameters\n", "rf_model = RandomForestClassifier(random_state=42)\n", "\n", "rf_param_grid = {\n", "    'n_estimators': [50, 100, 200],  # Number of trees in the forest\n", "    'max_depth': [3, 5, 7, 9],  # Maximum depth of the tree (None means nodes are expanded until all leaves are pure)\n", "    #'min_samples_split': [2, 5, 10],  # Minimum number of samples required to split an internal node\n", "    #'min_samples_leaf': [1, 2, 4],  # Minimum number of samples required to be at a leaf node\n", "    #'max_features': ['auto', 'sqrt', 'log2'],  # Number of features to consider when looking for the best split\n", "    'bootstrap': [True],  # Whether bootstrap samples are used when building trees\n", "    #'class_weight': [None, 'balanced', 'balanced_subsample']  # Weighing of classes in case of class imbalance\n", "}\n", "\n", "xgb_model = xgb.XGBClassifier(tree_method = \"hist\", \n", "                              eval_metric='logloss',\n", "                              device = \"cuda\",  # Use GPU for training\n", "                              response_method = None\n", "                              )\n", "xgb_param_grid = {\n", "    'n_estimators': [50, 100, 150, 200],\n", "    'max_depth': [3, 5, 7, 9],\n", "    'learning_rate': [0.01, 0.1, 0.2],  # Learning rate\n", "}\n", "\n", "# HistGradientBoostingClassifier Model\n", "hgb_model = HistGradientBoostingClassifier(random_state=42)\n", "\n", "# Hyperparameter grid for HistGradientBoostingClassifier\n", "hgb_param_grid = {\n", "    'learning_rate': [0.01, 0.1, 0.2],  # Learning rate\n", "    'max_iter': [100, 200, 300],  # Number of boosting iterations\n", "    'max_depth': [3, 5, 7],  # Maximum depth of the tree\n", "    #'min_samples_leaf': [10, 20, 30],  # Minimum number of samples required to be at a leaf node\n", "    'l2_regularization': [0.0, 0.1, 0.5],  # L2 regularization strength\n", "    #'max_bins': [255, 511],  # Maximum number of bins used for discretizing features\n", "    'early_stopping': [True]\n", "}"]}, {"cell_type": "code", "execution_count": 69, "metadata": {}, "outputs": [], "source": ["# Define scoring metrics\n", "accuracy_scorer = make_scorer(accuracy_score)\n", "precision_scorer = make_scorer(precision_score)\n", "f1_scorer = make_scorer(f1_score)\n", "roc_auc_scorer = make_scorer(roc_auc_score, greater_is_better=True, response_method='predict_proba')"]}, {"cell_type": "code", "execution_count": 70, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Evaluating XGBoost...\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[70], line 26\u001b[0m\n\u001b[0;32m     23\u001b[0m grid_search \u001b[38;5;241m=\u001b[39m HalvingGridSearchCV(estimator\u001b[38;5;241m=\u001b[39mmodel, param_grid\u001b[38;5;241m=\u001b[39mparam_grid, cv\u001b[38;5;241m=\u001b[39mcv, scoring\u001b[38;5;241m=\u001b[39maccuracy_scorer, verbose\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m0\u001b[39m)\n\u001b[0;32m     25\u001b[0m \u001b[38;5;66;03m# Fit the grid search on the whole dataset to get the best parameters\u001b[39;00m\n\u001b[1;32m---> 26\u001b[0m \u001b[43mgrid_search\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfit\u001b[49m\u001b[43m(\u001b[49m\u001b[43mX\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43my\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m     28\u001b[0m \u001b[38;5;66;03m# Get cross-validated score\u001b[39;00m\n\u001b[0;32m     29\u001b[0m cv_score \u001b[38;5;241m=\u001b[39m cross_val_score(grid_search\u001b[38;5;241m.\u001b[39mbest_estimator_, X, y, cv\u001b[38;5;241m=\u001b[39mcv, scoring\u001b[38;5;241m=\u001b[39maccuracy_scorer)\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\aifootballpredictions_notebooks\\lib\\site-packages\\sklearn\\base.py:1473\u001b[0m, in \u001b[0;36m_fit_context.<locals>.decorator.<locals>.wrapper\u001b[1;34m(estimator, *args, **kwargs)\u001b[0m\n\u001b[0;32m   1466\u001b[0m     estimator\u001b[38;5;241m.\u001b[39m_validate_params()\n\u001b[0;32m   1468\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m config_context(\n\u001b[0;32m   1469\u001b[0m     skip_parameter_validation\u001b[38;5;241m=\u001b[39m(\n\u001b[0;32m   1470\u001b[0m         prefer_skip_nested_validation \u001b[38;5;129;01mor\u001b[39;00m global_skip_validation\n\u001b[0;32m   1471\u001b[0m     )\n\u001b[0;32m   1472\u001b[0m ):\n\u001b[1;32m-> 1473\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m fit_method(estimator, \u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\aifootballpredictions_notebooks\\lib\\site-packages\\sklearn\\model_selection\\_search_successive_halving.py:251\u001b[0m, in \u001b[0;36mBaseSuccessiveHalving.fit\u001b[1;34m(self, X, y, **params)\u001b[0m\n\u001b[0;32m    245\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_check_input_parameters(\n\u001b[0;32m    246\u001b[0m     X\u001b[38;5;241m=\u001b[39mX, y\u001b[38;5;241m=\u001b[39my, split_params\u001b[38;5;241m=\u001b[39mrouted_params\u001b[38;5;241m.\u001b[39msplitter\u001b[38;5;241m.\u001b[39msplit\n\u001b[0;32m    247\u001b[0m )\n\u001b[0;32m    249\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_n_samples_orig \u001b[38;5;241m=\u001b[39m _num_samples(X)\n\u001b[1;32m--> 251\u001b[0m \u001b[38;5;28msuper\u001b[39m()\u001b[38;5;241m.\u001b[39mfit(X, y\u001b[38;5;241m=\u001b[39my, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mparams)\n\u001b[0;32m    253\u001b[0m \u001b[38;5;66;03m# Set best_score_: BaseSearchCV does not set it, as refit is a callable\u001b[39;00m\n\u001b[0;32m    254\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mbest_score_ \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcv_results_[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmean_test_score\u001b[39m\u001b[38;5;124m\"\u001b[39m][\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mbest_index_]\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\aifootballpredictions_notebooks\\lib\\site-packages\\sklearn\\base.py:1473\u001b[0m, in \u001b[0;36m_fit_context.<locals>.decorator.<locals>.wrapper\u001b[1;34m(estimator, *args, **kwargs)\u001b[0m\n\u001b[0;32m   1466\u001b[0m     estimator\u001b[38;5;241m.\u001b[39m_validate_params()\n\u001b[0;32m   1468\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m config_context(\n\u001b[0;32m   1469\u001b[0m     skip_parameter_validation\u001b[38;5;241m=\u001b[39m(\n\u001b[0;32m   1470\u001b[0m         prefer_skip_nested_validation \u001b[38;5;129;01mor\u001b[39;00m global_skip_validation\n\u001b[0;32m   1471\u001b[0m     )\n\u001b[0;32m   1472\u001b[0m ):\n\u001b[1;32m-> 1473\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m fit_method(estimator, \u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\aifootballpredictions_notebooks\\lib\\site-packages\\sklearn\\model_selection\\_search.py:1018\u001b[0m, in \u001b[0;36mBaseSearchCV.fit\u001b[1;34m(self, X, y, **params)\u001b[0m\n\u001b[0;32m   1012\u001b[0m     results \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_format_results(\n\u001b[0;32m   1013\u001b[0m         all_candidate_params, n_splits, all_out, all_more_results\n\u001b[0;32m   1014\u001b[0m     )\n\u001b[0;32m   1016\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m results\n\u001b[1;32m-> 1018\u001b[0m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_run_search\u001b[49m\u001b[43m(\u001b[49m\u001b[43mevaluate_candidates\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   1020\u001b[0m \u001b[38;5;66;03m# multimetric is determined here because in the case of a callable\u001b[39;00m\n\u001b[0;32m   1021\u001b[0m \u001b[38;5;66;03m# self.scoring the return type is only known after calling\u001b[39;00m\n\u001b[0;32m   1022\u001b[0m first_test_score \u001b[38;5;241m=\u001b[39m all_out[\u001b[38;5;241m0\u001b[39m][\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtest_scores\u001b[39m\u001b[38;5;124m\"\u001b[39m]\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\aifootballpredictions_notebooks\\lib\\site-packages\\sklearn\\model_selection\\_search_successive_halving.py:355\u001b[0m, in \u001b[0;36mBaseSuccessiveHalving._run_search\u001b[1;34m(self, evaluate_candidates)\u001b[0m\n\u001b[0;32m    348\u001b[0m     cv \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_checked_cv_orig\n\u001b[0;32m    350\u001b[0m more_results \u001b[38;5;241m=\u001b[39m {\n\u001b[0;32m    351\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124miter\u001b[39m\u001b[38;5;124m\"\u001b[39m: [itr] \u001b[38;5;241m*\u001b[39m n_candidates,\n\u001b[0;32m    352\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mn_resources\u001b[39m\u001b[38;5;124m\"\u001b[39m: [n_resources] \u001b[38;5;241m*\u001b[39m n_candidates,\n\u001b[0;32m    353\u001b[0m }\n\u001b[1;32m--> 355\u001b[0m results \u001b[38;5;241m=\u001b[39m \u001b[43mevaluate_candidates\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    356\u001b[0m \u001b[43m    \u001b[49m\u001b[43mcandidate_params\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcv\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmore_results\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmore_results\u001b[49m\n\u001b[0;32m    357\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    359\u001b[0m n_candidates_to_keep \u001b[38;5;241m=\u001b[39m ceil(n_candidates \u001b[38;5;241m/\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mfactor)\n\u001b[0;32m    360\u001b[0m candidate_params \u001b[38;5;241m=\u001b[39m _top_k(results, n_candidates_to_keep, itr)\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\aifootballpredictions_notebooks\\lib\\site-packages\\sklearn\\model_selection\\_search.py:964\u001b[0m, in \u001b[0;36mBaseSearchCV.fit.<locals>.evaluate_candidates\u001b[1;34m(candidate_params, cv, more_results)\u001b[0m\n\u001b[0;32m    956\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mverbose \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m0\u001b[39m:\n\u001b[0;32m    957\u001b[0m     \u001b[38;5;28mprint\u001b[39m(\n\u001b[0;32m    958\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mFitting \u001b[39m\u001b[38;5;132;01m{0}\u001b[39;00m\u001b[38;5;124m folds for each of \u001b[39m\u001b[38;5;132;01m{1}\u001b[39;00m\u001b[38;5;124m candidates,\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    959\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m totalling \u001b[39m\u001b[38;5;132;01m{2}\u001b[39;00m\u001b[38;5;124m fits\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;241m.\u001b[39mformat(\n\u001b[0;32m    960\u001b[0m             n_splits, n_candidates, n_candidates \u001b[38;5;241m*\u001b[39m n_splits\n\u001b[0;32m    961\u001b[0m         )\n\u001b[0;32m    962\u001b[0m     )\n\u001b[1;32m--> 964\u001b[0m out \u001b[38;5;241m=\u001b[39m \u001b[43mparallel\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    965\u001b[0m \u001b[43m    \u001b[49m\u001b[43mdelayed\u001b[49m\u001b[43m(\u001b[49m\u001b[43m_fit_and_score\u001b[49m\u001b[43m)\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    966\u001b[0m \u001b[43m        \u001b[49m\u001b[43mclone\u001b[49m\u001b[43m(\u001b[49m\u001b[43mbase_estimator\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    967\u001b[0m \u001b[43m        \u001b[49m\u001b[43mX\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    968\u001b[0m \u001b[43m        \u001b[49m\u001b[43my\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    969\u001b[0m \u001b[43m        \u001b[49m\u001b[43mtrain\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtrain\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    970\u001b[0m \u001b[43m        \u001b[49m\u001b[43mtest\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtest\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    971\u001b[0m \u001b[43m        \u001b[49m\u001b[43mparameters\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mparameters\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    972\u001b[0m \u001b[43m        \u001b[49m\u001b[43msplit_progress\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43msplit_idx\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mn_splits\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    973\u001b[0m \u001b[43m        \u001b[49m\u001b[43mcandidate_progress\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mcand_idx\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mn_candidates\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    974\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mfit_and_score_kwargs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    975\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    976\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43m(\u001b[49m\u001b[43mcand_idx\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mparameters\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m(\u001b[49m\u001b[43msplit_idx\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m(\u001b[49m\u001b[43mtrain\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtest\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mproduct\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    977\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;28;43menumerate\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mcandidate_params\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    978\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;28;43menumerate\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mcv\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msplit\u001b[49m\u001b[43m(\u001b[49m\u001b[43mX\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43my\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mrouted_params\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msplitter\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msplit\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    979\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    980\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    982\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(out) \u001b[38;5;241m<\u001b[39m \u001b[38;5;241m1\u001b[39m:\n\u001b[0;32m    983\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\n\u001b[0;32m    984\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mNo fits were performed. \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    985\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mWas the CV iterator empty? \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    986\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mWere there no candidates?\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    987\u001b[0m     )\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\aifootballpredictions_notebooks\\lib\\site-packages\\sklearn\\utils\\parallel.py:74\u001b[0m, in \u001b[0;36mParallel.__call__\u001b[1;34m(self, iterable)\u001b[0m\n\u001b[0;32m     69\u001b[0m config \u001b[38;5;241m=\u001b[39m get_config()\n\u001b[0;32m     70\u001b[0m iterable_with_config \u001b[38;5;241m=\u001b[39m (\n\u001b[0;32m     71\u001b[0m     (_with_config(delayed_func, config), args, kwargs)\n\u001b[0;32m     72\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m delayed_func, args, kwargs \u001b[38;5;129;01min\u001b[39;00m iterable\n\u001b[0;32m     73\u001b[0m )\n\u001b[1;32m---> 74\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[38;5;21;43m__call__\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43miterable_with_config\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\aifootballpredictions_notebooks\\lib\\site-packages\\joblib\\parallel.py:1918\u001b[0m, in \u001b[0;36mParallel.__call__\u001b[1;34m(self, iterable)\u001b[0m\n\u001b[0;32m   1916\u001b[0m     output \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_get_sequential_output(iterable)\n\u001b[0;32m   1917\u001b[0m     \u001b[38;5;28mnext\u001b[39m(output)\n\u001b[1;32m-> 1918\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m output \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mreturn_generator \u001b[38;5;28;01melse\u001b[39;00m \u001b[38;5;28;43mlist\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43moutput\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   1920\u001b[0m \u001b[38;5;66;03m# Let's create an ID that uniquely identifies the current call. If the\u001b[39;00m\n\u001b[0;32m   1921\u001b[0m \u001b[38;5;66;03m# call is interrupted early and that the same instance is immediately\u001b[39;00m\n\u001b[0;32m   1922\u001b[0m \u001b[38;5;66;03m# re-used, this id will be used to prevent workers that were\u001b[39;00m\n\u001b[0;32m   1923\u001b[0m \u001b[38;5;66;03m# concurrently finalizing a task from the previous call to run the\u001b[39;00m\n\u001b[0;32m   1924\u001b[0m \u001b[38;5;66;03m# callback.\u001b[39;00m\n\u001b[0;32m   1925\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_lock:\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\aifootballpredictions_notebooks\\lib\\site-packages\\joblib\\parallel.py:1847\u001b[0m, in \u001b[0;36mParallel._get_sequential_output\u001b[1;34m(self, iterable)\u001b[0m\n\u001b[0;32m   1845\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mn_dispatched_batches \u001b[38;5;241m+\u001b[39m\u001b[38;5;241m=\u001b[39m \u001b[38;5;241m1\u001b[39m\n\u001b[0;32m   1846\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mn_dispatched_tasks \u001b[38;5;241m+\u001b[39m\u001b[38;5;241m=\u001b[39m \u001b[38;5;241m1\u001b[39m\n\u001b[1;32m-> 1847\u001b[0m res \u001b[38;5;241m=\u001b[39m func(\u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n\u001b[0;32m   1848\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mn_completed_tasks \u001b[38;5;241m+\u001b[39m\u001b[38;5;241m=\u001b[39m \u001b[38;5;241m1\u001b[39m\n\u001b[0;32m   1849\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mprint_progress()\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\aifootballpredictions_notebooks\\lib\\site-packages\\sklearn\\utils\\parallel.py:136\u001b[0m, in \u001b[0;36m_FuncWrapper.__call__\u001b[1;34m(self, *args, **kwargs)\u001b[0m\n\u001b[0;32m    134\u001b[0m     config \u001b[38;5;241m=\u001b[39m {}\n\u001b[0;32m    135\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m config_context(\u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mconfig):\n\u001b[1;32m--> 136\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mfunction(\u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\aifootballpredictions_notebooks\\lib\\site-packages\\sklearn\\model_selection\\_validation.py:888\u001b[0m, in \u001b[0;36m_fit_and_score\u001b[1;34m(estimator, X, y, scorer, train, test, verbose, parameters, fit_params, score_params, return_train_score, return_parameters, return_n_test_samples, return_times, return_estimator, split_progress, candidate_progress, error_score)\u001b[0m\n\u001b[0;32m    886\u001b[0m         estimator\u001b[38;5;241m.\u001b[39mfit(X_train, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mfit_params)\n\u001b[0;32m    887\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m--> 888\u001b[0m         estimator\u001b[38;5;241m.\u001b[39mfit(X_train, y_train, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mfit_params)\n\u001b[0;32m    890\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m:\n\u001b[0;32m    891\u001b[0m     \u001b[38;5;66;03m# Note fit time as time until error\u001b[39;00m\n\u001b[0;32m    892\u001b[0m     fit_time \u001b[38;5;241m=\u001b[39m time\u001b[38;5;241m.\u001b[39mtime() \u001b[38;5;241m-\u001b[39m start_time\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\aifootballpredictions_notebooks\\lib\\site-packages\\xgboost\\core.py:726\u001b[0m, in \u001b[0;36mrequire_keyword_args.<locals>.throw_if.<locals>.inner_f\u001b[1;34m(*args, **kwargs)\u001b[0m\n\u001b[0;32m    724\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m k, arg \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mzip\u001b[39m(sig\u001b[38;5;241m.\u001b[39mparameters, args):\n\u001b[0;32m    725\u001b[0m     kwargs[k] \u001b[38;5;241m=\u001b[39m arg\n\u001b[1;32m--> 726\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m func(\u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\aifootballpredictions_notebooks\\lib\\site-packages\\xgboost\\sklearn.py:1531\u001b[0m, in \u001b[0;36mXGBClassifier.fit\u001b[1;34m(self, X, y, sample_weight, base_margin, eval_set, verbose, xgb_model, sample_weight_eval_set, base_margin_eval_set, feature_weights)\u001b[0m\n\u001b[0;32m   1511\u001b[0m model, metric, params \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_configure_fit(xgb_model, params)\n\u001b[0;32m   1512\u001b[0m train_dmatrix, evals \u001b[38;5;241m=\u001b[39m _wrap_evaluation_matrices(\n\u001b[0;32m   1513\u001b[0m     missing\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mmissing,\n\u001b[0;32m   1514\u001b[0m     X\u001b[38;5;241m=\u001b[39mX,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m   1528\u001b[0m     feature_types\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mfeature_types,\n\u001b[0;32m   1529\u001b[0m )\n\u001b[1;32m-> 1531\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_Booster \u001b[38;5;241m=\u001b[39m \u001b[43mtrain\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m   1532\u001b[0m \u001b[43m    \u001b[49m\u001b[43mparams\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1533\u001b[0m \u001b[43m    \u001b[49m\u001b[43mtrain_dmatrix\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1534\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_num_boosting_rounds\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1535\u001b[0m \u001b[43m    \u001b[49m\u001b[43mevals\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mevals\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1536\u001b[0m \u001b[43m    \u001b[49m\u001b[43mearly_stopping_rounds\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mearly_stopping_rounds\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1537\u001b[0m \u001b[43m    \u001b[49m\u001b[43mevals_result\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mevals_result\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1538\u001b[0m \u001b[43m    \u001b[49m\u001b[43mobj\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mobj\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1539\u001b[0m \u001b[43m    \u001b[49m\u001b[43mcustom_metric\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmetric\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1540\u001b[0m \u001b[43m    \u001b[49m\u001b[43mverbose_eval\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mverbose\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1541\u001b[0m \u001b[43m    \u001b[49m\u001b[43mxgb_model\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmodel\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1542\u001b[0m \u001b[43m    \u001b[49m\u001b[43mcallbacks\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcallbacks\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1543\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   1545\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28mcallable\u001b[39m(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mobjective):\n\u001b[0;32m   1546\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mobjective \u001b[38;5;241m=\u001b[39m params[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mobjective\u001b[39m\u001b[38;5;124m\"\u001b[39m]\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\aifootballpredictions_notebooks\\lib\\site-packages\\xgboost\\core.py:726\u001b[0m, in \u001b[0;36mrequire_keyword_args.<locals>.throw_if.<locals>.inner_f\u001b[1;34m(*args, **kwargs)\u001b[0m\n\u001b[0;32m    724\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m k, arg \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mzip\u001b[39m(sig\u001b[38;5;241m.\u001b[39mparameters, args):\n\u001b[0;32m    725\u001b[0m     kwargs[k] \u001b[38;5;241m=\u001b[39m arg\n\u001b[1;32m--> 726\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m func(\u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\aifootballpredictions_notebooks\\lib\\site-packages\\xgboost\\training.py:181\u001b[0m, in \u001b[0;36mtrain\u001b[1;34m(params, dtrain, num_boost_round, evals, obj, feval, maximize, early_stopping_rounds, evals_result, verbose_eval, xgb_model, callbacks, custom_metric)\u001b[0m\n\u001b[0;32m    179\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m cb_container\u001b[38;5;241m.\u001b[39mbefore_iteration(bst, i, dtrain, evals):\n\u001b[0;32m    180\u001b[0m     \u001b[38;5;28;01mbreak\u001b[39;00m\n\u001b[1;32m--> 181\u001b[0m \u001b[43mbst\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mupdate\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdtrain\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43miteration\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mi\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mfobj\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mobj\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    182\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m cb_container\u001b[38;5;241m.\u001b[39mafter_iteration(bst, i, dtrain, evals):\n\u001b[0;32m    183\u001b[0m     \u001b[38;5;28;01mbreak\u001b[39;00m\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\aifootballpredictions_notebooks\\lib\\site-packages\\xgboost\\core.py:2101\u001b[0m, in \u001b[0;36mBooster.update\u001b[1;34m(self, dtrain, iteration, fobj)\u001b[0m\n\u001b[0;32m   2097\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_assign_dmatrix_features(dtrain)\n\u001b[0;32m   2099\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m fobj \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[0;32m   2100\u001b[0m     _check_call(\n\u001b[1;32m-> 2101\u001b[0m         \u001b[43m_LIB\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mXGBoosterUpdateOneIter\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m   2102\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mhandle\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mctypes\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mc_int\u001b[49m\u001b[43m(\u001b[49m\u001b[43miteration\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdtrain\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mhandle\u001b[49m\n\u001b[0;32m   2103\u001b[0m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   2104\u001b[0m     )\n\u001b[0;32m   2105\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m   2106\u001b[0m     pred \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mpredict(dtrain, output_margin\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m, training\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m)\n", "\u001b[1;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["# 10-fold cross-validation\n", "cv = KFold(n_splits=10, shuffle=True, random_state=42)\n", "\n", "# Combine the models and hyperparameters into a dictionary\n", "models = {\n", "    'XGBoost': (xgb_model, xgb_param_grid),\n", "    'HistGradientBoosting': (hgb_model, hgb_param_grid),\n", "    #'LSTM': (lstm_model, lstm_param_grid),\n", "    #'Neural Network': (dnn_model, dnn_param_grid),\n", "    'Logistic Regression': (lr_model, lr_param_grid),\n", "    'KNN': (knn_model, knn_param_grid),\n", "    'SVM': (svm_model, svm_param_grid),\n", "    'Random Forest': (rf_model, rf_param_grid),\n", "}\n", "\n", "results = {}\n", "best_params = {}\n", "\n", "for model_name, (model, param_grid) in models.items():\n", "    print(f\"Evaluating {model_name}...\")\n", "    \n", "    # Initialize HalvingGridSearchCV with the inner cross-validation and hyperparameter grid\n", "    grid_search = HalvingGridSearchCV(estimator=model, param_grid=param_grid, cv=cv, scoring=accuracy_scorer, verbose=0)\n", "        \n", "    # Fit the grid search on the whole dataset to get the best parameters\n", "    grid_search.fit(X, y)\n", "\n", "    # Get cross-validated score\n", "    cv_score = cross_val_score(grid_search.best_estimator_, X, y, cv=cv, scoring=accuracy_scorer)\n", "    \n", "    # Store the results and best parameters\n", "    results[model_name] = cv_score\n", "    best_params[model_name] = grid_search.best_params_\n", "    \n", "    print(f\"{model_name} - {accuracy_scorer._score_func.__name__}: {np.mean(cv_score):.4f} ± {np.std(cv_score):.4f}\")\n", "    print(f\"Best parameters for {model_name}: {grid_search.best_params_}\")"]}, {"cell_type": "code", "execution_count": 64, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Best parameters for XGBoost: {'learning_rate': 0.2, 'max_depth': 3, 'n_estimators': 150}\n", "Best parameters for HistGradientBoosting: {'early_stopping': True, 'l2_regularization': 0.1, 'learning_rate': 0.1, 'max_depth': 3, 'max_iter': 100}\n", "Best parameters for Logistic Regression: {'C': 10, 'max_iter': 2000, 'penalty': 'l2'}\n", "Best parameters for KNN: {'metric': 'manhattan', 'n_neighbors': 7, 'weights': 'uniform'}\n", "Best parameters for SVM: {'C': 0.1, 'class_weight': None, 'degree': 2, 'gamma': 'scale', 'kernel': 'linear'}\n", "Best parameters for Random Forest: {'bootstrap': True, 'max_depth': 9, 'n_estimators': 100}\n"]}], "source": ["# Show the best parameters for each model\n", "for model_name, params in best_params.items():\n", "    print(f\"Best parameters for {model_name}: {params}\")"]}, {"cell_type": "code", "execution_count": 65, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Model Comparison accuracy_score:\n", "XGBoost: 0.7711 ± 0.0417\n", "HistGradientBoosting: 0.8013 ± 0.0438\n", "Logistic Regression: 0.8053 ± 0.0407\n", "KNN: 0.7750 ± 0.0401\n", "SVM: 0.8132 ± 0.0347\n", "Random Forest: 0.7974 ± 0.0409\n"]}], "source": ["# Compare models\n", "print(f\"\\nModel Comparison {accuracy_scorer._score_func.__name__}:\")\n", "for model_name, scores in results.items():\n", "    print(f\"{model_name}: {np.mean(scores):.4f} ± {np.std(scores):.4f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- First Run hyperparameters selection\n", "Best parameters for XGBoost: {'learning_rate': 0.1, 'max_depth': 3, 'n_estimators': 50}\n", "Best parameters for Logistic Regression: {'C': 0.1, 'penalty': 'l2'}\n", "Best parameters for KNN: {'metric': 'manhattan', 'n_neighbors': 9, 'weights': 'distance'}\n", "Best parameters for SVM: {'C': 0.1, 'gamma': 'scale', 'kernel': 'linear'}\n", "\n", "Model Comparison accuracy_score:\n", "XGBoost: 0.7740 ± 0.0378\n", "Logistic Regression: 0.8125 ± 0.0196\n", "KNN: 0.7634 ± 0.0317\n", "SVM: 0.8058 ± 0.0308\n", "\n", "- Second Run hyperparameters selection\n", "Best parameters for XGBoost: {'colsample_bytree': 0.6, 'learning_rate': 0.2, 'max_depth': 3, 'n_estimators': 200}\n", "Best parameters for Logistic Regression: {'C': 0.1, 'penalty': 'l2', 'solver': 'liblinear'}\n", "Best parameters for KNN: {'metric': 'manhattan', 'n_neighbors': 9, 'weights': 'distance'}\n", "Best parameters for SVM: {'C': 10, 'class_weight': 'balanced', 'degree': 2, 'gamma': 'scale', 'kernel': 'rbf'}\n", "\n", "Model Comparison accuracy_score:\n", "XGBoost: 0.7740 ± 0.0267\n", "Logistic Regression: 0.7966 ± 0.0281\n", "KNN: 0.7713 ± 0.0328\n", "SVM: 0.8125 ± 0.0233\n", "\n", "- Third Run\n", "Best parameters for XGBoost: {'max_depth': 3, 'n_estimators': 50}\n", "Best parameters for Logistic Regression: {'C': 1, 'penalty': 'l1', 'solver': 'liblinear'}\n", "Best parameters for KNN: {'metric': 'manhattan', 'n_neighbors': 5, 'weights': 'uniform'}\n", "Best parameters for SVM: {'C': 10, 'class_weight': None, 'degree': 3, 'gamma': 'scale', 'kernel': 'poly'}\n", "Best parameters for Random Forest: {'max_depth': 7, 'n_estimators': 200}\n", "\n", "Model Comparison accuracy_score:\n", "XGBoost: 0.7674 ± 0.0411\n", "Logistic Regression: 0.8125 ± 0.0218\n", "KNN: 0.7514 ± 0.0286\n", "SVM: 0.8125 ± 0.0306\n", "Random Forest: 0.7699 ± 0.0291\n", "\n", "- Fourth Run (feature scaling + maximum variance feature selected in clustering)\n", "Model Comparison accuracy_score:\n", "XGBoost: 0.5585 ± 0.0375\n", "Logistic Regression: 0.5851 ± 0.0096\n", "KNN: 0.5492 ± 0.0492\n", "SVM: 0.5664 ± 0.0405\n", "Random Forest: 0.5864 ± 0.0057"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Ensamble learning"]}, {"cell_type": "code", "execution_count": 67, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Voting Classifier - accuracy_score: 0.8092 ± 0.0429\n"]}], "source": ["# Initialize the models with the best hyperparameters\n", "best_lr_model = LogisticRegression(**best_params['Logistic Regression'])\n", "best_knn_model = KNeighborsClassifier(**best_params['KNN'])\n", "best_svm_model = SVC(**best_params['SVM'], probability=True)\n", "best_rf_model = RandomForestClassifier(**best_params['Random Forest'])\n", "best_xgb_model = xgb.XGBClassifier(**best_params['XGBoost'])\n", "best_hgb_model = HistGradientBoostingClassifier(**best_params['HistGradientBoosting'])\n", "\n", "# Combine the models into a voting classifier\n", "voting_clf = VotingClassifier(estimators=[\n", "    ('lr', best_lr_model),\n", "    ('knn', best_knn_model),\n", "    ('svm', best_svm_model),\n", "    ('rf', best_rf_model),\n", "    ('xgb', best_xgb_model),\n", "    ('hgb', best_hgb_model)\n", "], voting='soft')  # 'soft' for probability-based voting, 'hard' for majority voting\n", "\n", "# Fit the voting classifier\n", "voting_clf.fit(X, y)\n", "\n", "# Evaluate the ensemble using cross-validation\n", "cv_scores = cross_val_score(voting_clf, X, y, cv=10, scoring=accuracy_scorer)\n", "print(f\"Voting Classifier - {accuracy_scorer._score_func.__name__}: {np.mean(cv_scores):.4f} ± {np.std(cv_scores):.4f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Bayesian Search for Hyp Tuning"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import warnings\n", "from sklearn.exceptions import ConvergenceWarning\n", "\n", "# Suppress the ConvergenceWarning\n", "warnings.filterwarnings(\"ignore\", category=ConvergenceWarning)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Evaluating Logistic Regression...\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[6], line 79\u001b[0m\n\u001b[0;32m     76\u001b[0m bayes_search \u001b[38;5;241m=\u001b[39m BayesSearchCV(estimator\u001b[38;5;241m=\u001b[39mmodel, search_spaces\u001b[38;5;241m=\u001b[39mparam_space, cv\u001b[38;5;241m=\u001b[39mcv, scoring\u001b[38;5;241m=\u001b[39mscorer, n_iter\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m50\u001b[39m, random_state\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m42\u001b[39m, n_jobs\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m-\u001b[39m\u001b[38;5;241m1\u001b[39m, verbose\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m0\u001b[39m)\n\u001b[0;32m     78\u001b[0m \u001b[38;5;66;03m# Fit the Bayesian search on the whole dataset to get the best parameters\u001b[39;00m\n\u001b[1;32m---> 79\u001b[0m \u001b[43mbayes_search\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfit\u001b[49m\u001b[43m(\u001b[49m\u001b[43mX\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43my\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m     81\u001b[0m \u001b[38;5;66;03m# Get cross-validated score\u001b[39;00m\n\u001b[0;32m     82\u001b[0m cv_score \u001b[38;5;241m=\u001b[39m cross_val_score(bayes_search\u001b[38;5;241m.\u001b[39mbest_estimator_, X, y, cv\u001b[38;5;241m=\u001b[39mcv, scoring\u001b[38;5;241m=\u001b[39mscorer)\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\aifootballpredictions_notebooks\\lib\\site-packages\\skopt\\searchcv.py:542\u001b[0m, in \u001b[0;36mBayesSearchCV.fit\u001b[1;34m(self, X, y, groups, callback, **fit_params)\u001b[0m\n\u001b[0;32m    535\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mcallable\u001b[39m(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mrefit):\n\u001b[0;32m    536\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\n\u001b[0;32m    537\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mBayesSearchCV doesn\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mt support a callable refit, \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    538\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mas it doesn\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mt define an implicit score to \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    539\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124moptimize\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    540\u001b[0m     )\n\u001b[1;32m--> 542\u001b[0m \u001b[38;5;28msuper\u001b[39m()\u001b[38;5;241m.\u001b[39mfit(X\u001b[38;5;241m=\u001b[39mX, y\u001b[38;5;241m=\u001b[39my, groups\u001b[38;5;241m=\u001b[39mgroups, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mfit_params)\n\u001b[0;32m    544\u001b[0m \u001b[38;5;66;03m# BaseSearchCV never ranked train scores,\u001b[39;00m\n\u001b[0;32m    545\u001b[0m \u001b[38;5;66;03m# but apparently we used to ship this (back-compat)\u001b[39;00m\n\u001b[0;32m    546\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mreturn_train_score:\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\aifootballpredictions_notebooks\\lib\\site-packages\\sklearn\\base.py:1473\u001b[0m, in \u001b[0;36m_fit_context.<locals>.decorator.<locals>.wrapper\u001b[1;34m(estimator, *args, **kwargs)\u001b[0m\n\u001b[0;32m   1466\u001b[0m     estimator\u001b[38;5;241m.\u001b[39m_validate_params()\n\u001b[0;32m   1468\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m config_context(\n\u001b[0;32m   1469\u001b[0m     skip_parameter_validation\u001b[38;5;241m=\u001b[39m(\n\u001b[0;32m   1470\u001b[0m         prefer_skip_nested_validation \u001b[38;5;129;01mor\u001b[39;00m global_skip_validation\n\u001b[0;32m   1471\u001b[0m     )\n\u001b[0;32m   1472\u001b[0m ):\n\u001b[1;32m-> 1473\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m fit_method(estimator, \u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\aifootballpredictions_notebooks\\lib\\site-packages\\sklearn\\model_selection\\_search.py:1018\u001b[0m, in \u001b[0;36mBaseSearchCV.fit\u001b[1;34m(self, X, y, **params)\u001b[0m\n\u001b[0;32m   1012\u001b[0m     results \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_format_results(\n\u001b[0;32m   1013\u001b[0m         all_candidate_params, n_splits, all_out, all_more_results\n\u001b[0;32m   1014\u001b[0m     )\n\u001b[0;32m   1016\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m results\n\u001b[1;32m-> 1018\u001b[0m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_run_search\u001b[49m\u001b[43m(\u001b[49m\u001b[43mevaluate_candidates\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   1020\u001b[0m \u001b[38;5;66;03m# multimetric is determined here because in the case of a callable\u001b[39;00m\n\u001b[0;32m   1021\u001b[0m \u001b[38;5;66;03m# self.scoring the return type is only known after calling\u001b[39;00m\n\u001b[0;32m   1022\u001b[0m first_test_score \u001b[38;5;241m=\u001b[39m all_out[\u001b[38;5;241m0\u001b[39m][\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtest_scores\u001b[39m\u001b[38;5;124m\"\u001b[39m]\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\aifootballpredictions_notebooks\\lib\\site-packages\\skopt\\searchcv.py:599\u001b[0m, in \u001b[0;36mBayesSearchCV._run_search\u001b[1;34m(self, evaluate_candidates)\u001b[0m\n\u001b[0;32m    595\u001b[0m \u001b[38;5;28;01mwhile\u001b[39;00m n_iter \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m0\u001b[39m:\n\u001b[0;32m    596\u001b[0m     \u001b[38;5;66;03m# when n_iter < n_points points left for evaluation\u001b[39;00m\n\u001b[0;32m    597\u001b[0m     n_points_adjusted \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mmin\u001b[39m(n_iter, n_points)\n\u001b[1;32m--> 599\u001b[0m     optim_result, score_name \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_step\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    600\u001b[0m \u001b[43m        \u001b[49m\u001b[43msearch_space\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    601\u001b[0m \u001b[43m        \u001b[49m\u001b[43moptimizer\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    602\u001b[0m \u001b[43m        \u001b[49m\u001b[43mscore_name\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    603\u001b[0m \u001b[43m        \u001b[49m\u001b[43mevaluate_candidates\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    604\u001b[0m \u001b[43m        \u001b[49m\u001b[43mn_points\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mn_points_adjusted\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    605\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    606\u001b[0m     n_iter \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m=\u001b[39m n_points\n\u001b[0;32m    608\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m eval_callbacks(callbacks, optim_result):\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\aifootballpredictions_notebooks\\lib\\site-packages\\skopt\\searchcv.py:482\u001b[0m, in \u001b[0;36mBayesSearchCV._step\u001b[1;34m(self, search_space, optimizer, score_name, evaluate_candidates, n_points)\u001b[0m\n\u001b[0;32m    479\u001b[0m local_results \u001b[38;5;241m=\u001b[39m all_results[score_name][\u001b[38;5;241m-\u001b[39m\u001b[38;5;28mlen\u001b[39m(params) :]\n\u001b[0;32m    480\u001b[0m \u001b[38;5;66;03m# return the score_name to cache it if callable refit\u001b[39;00m\n\u001b[0;32m    481\u001b[0m \u001b[38;5;66;03m# this avoids checking self.refit all the time\u001b[39;00m\n\u001b[1;32m--> 482\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m (\u001b[43moptimizer\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mtell\u001b[49m\u001b[43m(\u001b[49m\u001b[43mparams\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;241;43m-\u001b[39;49m\u001b[43mscore\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mscore\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mlocal_results\u001b[49m\u001b[43m]\u001b[49m\u001b[43m)\u001b[49m, score_name)\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\aifootballpredictions_notebooks\\lib\\site-packages\\skopt\\optimizer\\optimizer.py:570\u001b[0m, in \u001b[0;36mOptimizer.tell\u001b[1;34m(self, x, y, fit)\u001b[0m\n\u001b[0;32m    567\u001b[0m         y \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mlist\u001b[39m(y)\n\u001b[0;32m    568\u001b[0m         y[\u001b[38;5;241m1\u001b[39m] \u001b[38;5;241m=\u001b[39m log(y[\u001b[38;5;241m1\u001b[39m])\n\u001b[1;32m--> 570\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_tell\u001b[49m\u001b[43m(\u001b[49m\u001b[43mx\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43my\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mfit\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mfit\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\aifootballpredictions_notebooks\\lib\\site-packages\\skopt\\optimizer\\optimizer.py:615\u001b[0m, in \u001b[0;36mOptimizer._tell\u001b[1;34m(self, x, y, fit)\u001b[0m\n\u001b[0;32m    613\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m warnings\u001b[38;5;241m.\u001b[39mcatch_warnings():\n\u001b[0;32m    614\u001b[0m     warnings\u001b[38;5;241m.\u001b[39msimplefilter(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mignore\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m--> 615\u001b[0m     \u001b[43mest\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfit\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mspace\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mtransform\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mXi\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43myi\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    617\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mhasattr\u001b[39m(\u001b[38;5;28mself\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mnext_xs_\u001b[39m\u001b[38;5;124m\"\u001b[39m) \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39macq_func \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mgp_hedge\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n\u001b[0;32m    618\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mgains_ \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m=\u001b[39m est\u001b[38;5;241m.\u001b[39mpredict(np\u001b[38;5;241m.\u001b[39mvstack(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mnext_xs_))\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\aifootballpredictions_notebooks\\lib\\site-packages\\skopt\\learning\\gaussian_process\\gpr.py:203\u001b[0m, in \u001b[0;***************************.fit\u001b[1;34m(self, X, y)\u001b[0m\n\u001b[0;32m    199\u001b[0m     \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mnoise:\n\u001b[0;32m    200\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mkernel \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mkernel \u001b[38;5;241m+\u001b[39m <PERSON>(\n\u001b[0;32m    201\u001b[0m             noise_level\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mnoise, noise_level_bounds\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mfixed\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    202\u001b[0m         )\n\u001b[1;32m--> 203\u001b[0m \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfit\u001b[49m\u001b[43m(\u001b[49m\u001b[43mX\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43my\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    205\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mnoise_ \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[0;32m    207\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mnoise:\n\u001b[0;32m    208\u001b[0m     \u001b[38;5;66;03m# The noise component of this kernel should be set to zero\u001b[39;00m\n\u001b[0;32m    209\u001b[0m     \u001b[38;5;66;03m# while estimating K(X_test, X_test)\u001b[39;00m\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    214\u001b[0m     \u001b[38;5;66;03m# http://www.gaussianprocess.org/gpml/chapters/RW2.pdf\u001b[39;00m\n\u001b[0;32m    215\u001b[0m     \u001b[38;5;66;03m# Hence this hack\u001b[39;00m\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\aifootballpredictions_notebooks\\lib\\site-packages\\sklearn\\base.py:1473\u001b[0m, in \u001b[0;36m_fit_context.<locals>.decorator.<locals>.wrapper\u001b[1;34m(estimator, *args, **kwargs)\u001b[0m\n\u001b[0;32m   1466\u001b[0m     estimator\u001b[38;5;241m.\u001b[39m_validate_params()\n\u001b[0;32m   1468\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m config_context(\n\u001b[0;32m   1469\u001b[0m     skip_parameter_validation\u001b[38;5;241m=\u001b[39m(\n\u001b[0;32m   1470\u001b[0m         prefer_skip_nested_validation \u001b[38;5;129;01mor\u001b[39;00m global_skip_validation\n\u001b[0;32m   1471\u001b[0m     )\n\u001b[0;32m   1472\u001b[0m ):\n\u001b[1;32m-> 1473\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m fit_method(estimator, \u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\aifootballpredictions_notebooks\\lib\\site-packages\\sklearn\\gaussian_process\\_gpr.py:325\u001b[0m, in \u001b[0;***************************.fit\u001b[1;34m(self, X, y)\u001b[0m\n\u001b[0;32m    322\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m iteration \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mrange\u001b[39m(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mn_restarts_optimizer):\n\u001b[0;32m    323\u001b[0m         theta_initial \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_rng\u001b[38;5;241m.\u001b[39muniform(bounds[:, \u001b[38;5;241m0\u001b[39m], bounds[:, \u001b[38;5;241m1\u001b[39m])\n\u001b[0;32m    324\u001b[0m         optima\u001b[38;5;241m.\u001b[39mappend(\n\u001b[1;32m--> 325\u001b[0m             \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_constrained_optimization\u001b[49m\u001b[43m(\u001b[49m\u001b[43mobj_func\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtheta_initial\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mbounds\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    326\u001b[0m         )\n\u001b[0;32m    327\u001b[0m \u001b[38;5;66;03m# Select result from run with minimal (negative) log-marginal\u001b[39;00m\n\u001b[0;32m    328\u001b[0m \u001b[38;5;66;03m# likelihood\u001b[39;00m\n\u001b[0;32m    329\u001b[0m lml_values \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mlist\u001b[39m(\u001b[38;5;28mmap\u001b[39m(itemgetter(\u001b[38;5;241m1\u001b[39m), optima))\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\aifootballpredictions_notebooks\\lib\\site-packages\\sklearn\\gaussian_process\\_gpr.py:652\u001b[0m, in \u001b[0;***************************._constrained_optimization\u001b[1;34m(self, obj_func, initial_theta, bounds)\u001b[0m\n\u001b[0;32m    650\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m_constrained_optimization\u001b[39m(\u001b[38;5;28mself\u001b[39m, obj_func, initial_theta, bounds):\n\u001b[0;32m    651\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39moptimizer \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mfmin_l_bfgs_b\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n\u001b[1;32m--> 652\u001b[0m         opt_res \u001b[38;5;241m=\u001b[39m \u001b[43mscipy\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43moptimize\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mminimize\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    653\u001b[0m \u001b[43m            \u001b[49m\u001b[43mobj_func\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    654\u001b[0m \u001b[43m            \u001b[49m\u001b[43minitial_theta\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    655\u001b[0m \u001b[43m            \u001b[49m\u001b[43mmethod\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mL-BFGS-B\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[0;32m    656\u001b[0m \u001b[43m            \u001b[49m\u001b[43mjac\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[0;32m    657\u001b[0m \u001b[43m            \u001b[49m\u001b[43mbounds\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mbounds\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    658\u001b[0m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    659\u001b[0m         _check_optimize_result(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mlbfgs\u001b[39m\u001b[38;5;124m\"\u001b[39m, opt_res)\n\u001b[0;32m    660\u001b[0m         theta_opt, func_min \u001b[38;5;241m=\u001b[39m opt_res\u001b[38;5;241m.\u001b[39mx, opt_res\u001b[38;5;241m.\u001b[39mfun\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\aifootballpredictions_notebooks\\lib\\site-packages\\scipy\\optimize\\_minimize.py:731\u001b[0m, in \u001b[0;36mminimize\u001b[1;34m(fun, x0, args, method, jac, hess, hessp, bounds, constraints, tol, callback, options)\u001b[0m\n\u001b[0;32m    728\u001b[0m     res \u001b[38;5;241m=\u001b[39m _minimize_newtoncg(fun, x0, args, jac, hess, hessp, callback,\n\u001b[0;32m    729\u001b[0m                              \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39moptions)\n\u001b[0;32m    730\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m meth \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124ml-bfgs-b\u001b[39m\u001b[38;5;124m'\u001b[39m:\n\u001b[1;32m--> 731\u001b[0m     res \u001b[38;5;241m=\u001b[39m _minimize_lbfgsb(fun, x0, args, jac, bounds,\n\u001b[0;32m    732\u001b[0m                            callback\u001b[38;5;241m=\u001b[39mcallback, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39moptions)\n\u001b[0;32m    733\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m meth \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mtnc\u001b[39m\u001b[38;5;124m'\u001b[39m:\n\u001b[0;32m    734\u001b[0m     res \u001b[38;5;241m=\u001b[39m _minimize_tnc(fun, x0, args, jac, bounds, callback\u001b[38;5;241m=\u001b[39mcallback,\n\u001b[0;32m    735\u001b[0m                         \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39moptions)\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\aifootballpredictions_notebooks\\lib\\site-packages\\scipy\\optimize\\_lbfgsb_py.py:407\u001b[0m, in \u001b[0;36m_minimize_lbfgsb\u001b[1;34m(fun, x0, args, jac, bounds, disp, maxcor, ftol, gtol, eps, maxfun, maxiter, iprint, callback, maxls, finite_diff_rel_step, **unknown_options)\u001b[0m\n\u001b[0;32m    401\u001b[0m task_str \u001b[38;5;241m=\u001b[39m task\u001b[38;5;241m.\u001b[39mtobytes()\n\u001b[0;32m    402\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m task_str\u001b[38;5;241m.\u001b[39mstartswith(\u001b[38;5;124mb\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mFG\u001b[39m\u001b[38;5;124m'\u001b[39m):\n\u001b[0;32m    403\u001b[0m     \u001b[38;5;66;03m# The minimization routine wants f and g at the current x.\u001b[39;00m\n\u001b[0;32m    404\u001b[0m     \u001b[38;5;66;03m# Note that interruptions due to maxfun are postponed\u001b[39;00m\n\u001b[0;32m    405\u001b[0m     \u001b[38;5;66;03m# until the completion of the current minimization iteration.\u001b[39;00m\n\u001b[0;32m    406\u001b[0m     \u001b[38;5;66;03m# Overwrite f and g:\u001b[39;00m\n\u001b[1;32m--> 407\u001b[0m     f, g \u001b[38;5;241m=\u001b[39m \u001b[43mfunc_and_grad\u001b[49m\u001b[43m(\u001b[49m\u001b[43mx\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    408\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m task_str\u001b[38;5;241m.\u001b[39mstartswith(\u001b[38;5;124mb\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mNEW_X\u001b[39m\u001b[38;5;124m'\u001b[39m):\n\u001b[0;32m    409\u001b[0m     \u001b[38;5;66;03m# new iteration\u001b[39;00m\n\u001b[0;32m    410\u001b[0m     n_iterations \u001b[38;5;241m+\u001b[39m\u001b[38;5;241m=\u001b[39m \u001b[38;5;241m1\u001b[39m\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\aifootballpredictions_notebooks\\lib\\site-packages\\scipy\\optimize\\_differentiable_functions.py:343\u001b[0m, in \u001b[0;36mScalarFunction.fun_and_grad\u001b[1;34m(self, x)\u001b[0m\n\u001b[0;32m    341\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m np\u001b[38;5;241m.\u001b[39marray_equal(x, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mx):\n\u001b[0;32m    342\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_update_x(x)\n\u001b[1;32m--> 343\u001b[0m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_update_fun\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    344\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_update_grad()\n\u001b[0;32m    345\u001b[0m \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mf, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mg\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\aifootballpredictions_notebooks\\lib\\site-packages\\scipy\\optimize\\_differentiable_functions.py:294\u001b[0m, in \u001b[0;36mScalarFunction._update_fun\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m    292\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m_update_fun\u001b[39m(\u001b[38;5;28mself\u001b[39m):\n\u001b[0;32m    293\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mf_updated:\n\u001b[1;32m--> 294\u001b[0m         fx \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_wrapped_fun\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mx\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    295\u001b[0m         \u001b[38;5;28;01mif\u001b[39;00m fx \u001b[38;5;241m<\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_lowest_f:\n\u001b[0;32m    296\u001b[0m             \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_lowest_x \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mx\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\aifootballpredictions_notebooks\\lib\\site-packages\\scipy\\optimize\\_differentiable_functions.py:20\u001b[0m, in \u001b[0;36m_wrapper_fun.<locals>.wrapped\u001b[1;34m(x)\u001b[0m\n\u001b[0;32m     16\u001b[0m ncalls[\u001b[38;5;241m0\u001b[39m] \u001b[38;5;241m+\u001b[39m\u001b[38;5;241m=\u001b[39m \u001b[38;5;241m1\u001b[39m\n\u001b[0;32m     17\u001b[0m \u001b[38;5;66;03m# Send a copy because the user may overwrite it.\u001b[39;00m\n\u001b[0;32m     18\u001b[0m \u001b[38;5;66;03m# Overwriting results in undefined behaviour because\u001b[39;00m\n\u001b[0;32m     19\u001b[0m \u001b[38;5;66;03m# fun(self.x) will change self.x, with the two no longer linked.\u001b[39;00m\n\u001b[1;32m---> 20\u001b[0m fx \u001b[38;5;241m=\u001b[39m \u001b[43mfun\u001b[49m\u001b[43m(\u001b[49m\u001b[43mnp\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcopy\u001b[49m\u001b[43m(\u001b[49m\u001b[43mx\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m     21\u001b[0m \u001b[38;5;66;03m# Make sure the function returns a true scalar\u001b[39;00m\n\u001b[0;32m     22\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m np\u001b[38;5;241m.\u001b[39misscalar(fx):\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\aifootballpredictions_notebooks\\lib\\site-packages\\scipy\\optimize\\_optimize.py:79\u001b[0m, in \u001b[0;36mMemoizeJac.__call__\u001b[1;34m(self, x, *args)\u001b[0m\n\u001b[0;32m     77\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m__call__\u001b[39m(\u001b[38;5;28mself\u001b[39m, x, \u001b[38;5;241m*\u001b[39margs):\n\u001b[0;32m     78\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\" returns the function value \"\"\"\u001b[39;00m\n\u001b[1;32m---> 79\u001b[0m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_compute_if_needed\u001b[49m\u001b[43m(\u001b[49m\u001b[43mx\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m     80\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_value\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\aifootballpredictions_notebooks\\lib\\site-packages\\scipy\\optimize\\_optimize.py:73\u001b[0m, in \u001b[0;36mMemoizeJac._compute_if_needed\u001b[1;34m(self, x, *args)\u001b[0m\n\u001b[0;32m     71\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m np\u001b[38;5;241m.\u001b[39mall(x \u001b[38;5;241m==\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mx) \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_value \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mjac \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m:\n\u001b[0;32m     72\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mx \u001b[38;5;241m=\u001b[39m np\u001b[38;5;241m.\u001b[39masarray(x)\u001b[38;5;241m.\u001b[39mcopy()\n\u001b[1;32m---> 73\u001b[0m     fg \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfun\u001b[49m\u001b[43m(\u001b[49m\u001b[43mx\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m     74\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mjac \u001b[38;5;241m=\u001b[39m fg[\u001b[38;5;241m1\u001b[39m]\n\u001b[0;32m     75\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_value \u001b[38;5;241m=\u001b[39m fg[\u001b[38;5;241m0\u001b[39m]\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\aifootballpredictions_notebooks\\lib\\site-packages\\sklearn\\gaussian_process\\_gpr.py:297\u001b[0m, in \u001b[0;***************************.fit.<locals>.obj_func\u001b[1;34m(theta, eval_gradient)\u001b[0m\n\u001b[0;32m    295\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mobj_func\u001b[39m(theta, eval_gradient\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m):\n\u001b[0;32m    296\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m eval_gradient:\n\u001b[1;32m--> 297\u001b[0m         lml, grad \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mlog_marginal_likelihood\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    298\u001b[0m \u001b[43m            \u001b[49m\u001b[43mtheta\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43meval_gradient\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mclone_kernel\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\n\u001b[0;32m    299\u001b[0m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    300\u001b[0m         \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;241m-\u001b[39mlml, \u001b[38;5;241m-\u001b[39mgrad\n\u001b[0;32m    301\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\aifootballpredictions_notebooks\\lib\\site-packages\\sklearn\\gaussian_process\\_gpr.py:639\u001b[0m, in \u001b[0;***************************.log_marginal_likelihood\u001b[1;34m(self, theta, eval_gradient, clone_kernel)\u001b[0m\n\u001b[0;32m    628\u001b[0m inner_term \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m=\u001b[39m K_inv[\u001b[38;5;241m.\u001b[39m\u001b[38;5;241m.\u001b[39m\u001b[38;5;241m.\u001b[39m, np\u001b[38;5;241m.\u001b[39mnewaxis]\n\u001b[0;32m    629\u001b[0m \u001b[38;5;66;03m# Since we are interested about the trace of\u001b[39;00m\n\u001b[0;32m    630\u001b[0m \u001b[38;5;66;03m# inner_term @ K_gradient, we don't explicitly compute the\u001b[39;00m\n\u001b[0;32m    631\u001b[0m \u001b[38;5;66;03m# matrix-by-matrix operation and instead use an einsum. Therefore\u001b[39;00m\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    637\u001b[0m \u001b[38;5;66;03m#             K_gradient[..., param_idx]\u001b[39;00m\n\u001b[0;32m    638\u001b[0m \u001b[38;5;66;03m#         )\u001b[39;00m\n\u001b[1;32m--> 639\u001b[0m log_likelihood_gradient_dims \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m0.5\u001b[39m \u001b[38;5;241m*\u001b[39m \u001b[43mnp\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43meinsum\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    640\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mijl,jik->kl\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43minner_term\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mK_gradient\u001b[49m\n\u001b[0;32m    641\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    642\u001b[0m \u001b[38;5;66;03m# the log likehood gradient is the sum-up across the outputs\u001b[39;00m\n\u001b[0;32m    643\u001b[0m log_likelihood_gradient \u001b[38;5;241m=\u001b[39m log_likelihood_gradient_dims\u001b[38;5;241m.\u001b[39msum(axis\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m-\u001b[39m\u001b[38;5;241m1\u001b[39m)\n", "File \u001b[1;32m<__array_function__ internals>:180\u001b[0m, in \u001b[0;36meinsum\u001b[1;34m(*args, **kwargs)\u001b[0m\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\aifootballpredictions_notebooks\\lib\\site-packages\\numpy\\core\\einsumfunc.py:1371\u001b[0m, in \u001b[0;36meinsum\u001b[1;34m(out, optimize, *operands, **kwargs)\u001b[0m\n\u001b[0;32m   1369\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m specified_out:\n\u001b[0;32m   1370\u001b[0m         kwargs[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mout\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m=\u001b[39m out\n\u001b[1;32m-> 1371\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m c_einsum(\u001b[38;5;241m*\u001b[39moperands, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n\u001b[0;32m   1373\u001b[0m \u001b[38;5;66;03m# Check the kwargs to avoid a more cryptic error later, without having to\u001b[39;00m\n\u001b[0;32m   1374\u001b[0m \u001b[38;5;66;03m# repeat default values here\u001b[39;00m\n\u001b[0;32m   1375\u001b[0m valid_einsum_kwargs \u001b[38;5;241m=\u001b[39m [\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mdtype\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124morder\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mcasting\u001b[39m\u001b[38;5;124m'\u001b[39m]\n", "\u001b[1;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["# Define models and hyperparameters with Bayesian Optimization\n", "lr_model = LogisticRegression()\n", "lr_param_space = {\n", "    'C': Real(0.01, 10, prior='log-uniform'),\n", "    'penalty': Categorical(['l1', 'l2']),\n", "    'solver': Categorical(['liblinear', 'saga']),\n", "    'max_iter': <PERSON><PERSON><PERSON>(2000, 3000)\n", "}\n", "\n", "knn_model = KNeighborsClassifier()\n", "knn_param_space = {\n", "    'n_neighbors': <PERSON><PERSON><PERSON>(3, 9),\n", "    'weights': Categorical(['uniform', 'distance']),\n", "    'metric': Categorical(['euclidean', 'manhattan'])\n", "}\n", "\n", "svm_model = SVC(probability=True)\n", "svm_param_space = {\n", "    'C': Real(0.1, 10, prior='log-uniform'),\n", "    'kernel': Categorical(['linear', 'rbf', 'poly']),\n", "    'gamma': Categorical(['scale', 'auto']),\n", "    'degree': <PERSON><PERSON><PERSON>(2, 5),\n", "    'class_weight': Categorical([None, 'balanced'])\n", "}\n", "\n", "rf_model = RandomForestClassifier(random_state=42)\n", "rf_param_space = {\n", "    'n_estimators': <PERSON><PERSON><PERSON>(50, 200),\n", "    'max_depth': <PERSON><PERSON><PERSON>(3, 9),\n", "    'bootstrap': Categorical([True])\n", "}\n", "\n", "xgb_model = xgb.XGBClassifier(tree_method=\"hist\", eval_metric='logloss')\n", "xgb_param_space = {\n", "    'n_estimators': <PERSON><PERSON><PERSON>(50, 200),\n", "    'max_depth': <PERSON><PERSON><PERSON>(3, 9),\n", "    'learning_rate': Real(0.01, 0.2, prior='log-uniform')\n", "}\n", "\n", "hgb_model = HistGradientBoostingClassifier(random_state=42)\n", "hgb_param_space = {\n", "    'learning_rate': Real(0.01, 0.2, prior='log-uniform'),\n", "    'max_iter': <PERSON><PERSON><PERSON>(100, 300),\n", "    'max_depth': <PERSON><PERSON><PERSON>(3, 7),\n", "    'l2_regularization': Real(0.0, 0.5, prior='uniform'),\n", "    'early_stopping': Categorical([True])\n", "}\n", "\n", "# Define scoring metrics\n", "accuracy_scorer = make_scorer(accuracy_score)\n", "precision_scorer = make_scorer(precision_score)\n", "f1_scorer = make_scorer(f1_score)\n", "roc_auc_scorer = make_scorer(roc_auc_score, greater_is_better=True, response_method='predict_proba')\n", "scorer = make_scorer(accuracy_score)\n", "\n", "# 10-fold cross-validation\n", "cv = KFold(n_splits=10, shuffle=True, random_state=42)\n", "\n", "# Combine the models and hyperparameters into a dictionary\n", "models = {\n", "    'Logistic Regression': (lr_model, lr_param_space),\n", "    'KNN': (knn_model, knn_param_space),\n", "    'SVM': (svm_model, svm_param_space),\n", "    'Random Forest': (rf_model, rf_param_space),\n", "    'XGBoost': (xgb_model, xgb_param_space),\n", "    'HistGradientBoosting': (hgb_model, hgb_param_space),\n", "}\n", "\n", "results = {}\n", "best_params = {}\n", "\n", "for model_name, (model, param_space) in models.items():\n", "    print(f\"Evaluating {model_name}...\")\n", "\n", "    # Initialize BayesSearchCV with cross-validation and parameter space\n", "    bayes_search = BayesSearchCV(estimator=model, search_spaces=param_space, cv=cv, scoring=scorer, n_iter=50, random_state=42, n_jobs=-1, verbose=0)\n", "\n", "    # Fit the Bayesian search on the whole dataset to get the best parameters\n", "    bayes_search.fit(X, y)\n", "\n", "    # Get cross-validated score\n", "    cv_score = cross_val_score(bayes_search.best_estimator_, X, y, cv=cv, scoring=scorer)\n", "\n", "    # Store the results and best parameters\n", "    results[model_name] = cv_score\n", "    best_params[model_name] = bayes_search.best_params_\n", "\n", "    print(f\"{model_name} - {scorer._score_func.__name__}: {np.mean(cv_score):.4f} ± {np.std(cv_score):.4f}\")\n", "    print(f\"Best parameters for {model_name}: {bayes_search.best_params_}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Initialize the models with the best hyperparameters\n", "best_lr_model = LogisticRegression(**best_params['Logistic Regression'])\n", "best_knn_model = KNeighborsClassifier(**best_params['KNN'])\n", "best_svm_model = SVC(**best_params['SVM'], probability=True)\n", "best_rf_model = RandomForestClassifier(**best_params['Random Forest'])\n", "best_xgb_model = xgb.XGBClassifier(**best_params['XGBoost'])\n", "best_hgb_model = HistGradientBoostingClassifier(**best_params['HistGradientBoosting'])\n", "\n", "# Combine the models into a voting classifier\n", "voting_clf = VotingClassifier(estimators=[\n", "    ('lr', best_lr_model),\n", "    ('knn', best_knn_model),\n", "    ('svm', best_svm_model),\n", "    ('rf', best_rf_model),\n", "    ('xgb', best_xgb_model),\n", "    ('hgb', best_hgb_model)\n", "], voting='soft')  # 'soft' for probability-based voting, 'hard' for majority voting\n", "\n", "# Fit the voting classifier\n", "voting_clf.fit(X, y)\n", "\n", "# Evaluate the ensemble using cross-validation\n", "cv_scores = cross_val_score(voting_clf, X, y, cv=10, scoring=accuracy_scorer)\n", "print(f\"Voting Classifier - {accuracy_scorer._score_func.__name__}: {np.mean(cv_scores):.4f} ± {np.std(cv_scores):.4f}\")"]}], "metadata": {"kernelspec": {"display_name": "aifootballpredictions_notebooks", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 2}