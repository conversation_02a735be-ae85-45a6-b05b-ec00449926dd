"""
سكربت التحديث اليومي للتنبؤات
يقوم بتحديث المباريات وعمل تنبؤات جديدة
"""

import os
import json
from datetime import datetime, timedelta
import subprocess

def clean_old_predictions(predictions_file):
    """
    تنظيف التنبؤات القديمة والإبقاء على مباريات اليوم وغد فقط
    """
    try:
        with open(predictions_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        today = datetime.now()
        tomorrow = today + timedelta(days=1)
        today_str = today.strftime('%Y-%m-%d')
        tomorrow_str = tomorrow.strftime('%Y-%m-%d')
        
        # تنظيف المباريات القديمة من كل دوري
        for category in data.values():
            for league in category.values():
                if 'matches' in league:
                    # الاحتفاظ فقط بمباريات اليوم وغد
                    league['matches'] = [
                        match for match in league['matches']
                        if match['date'].split('T')[0] in [today_str, tomorrow_str]
                    ]
        
        # حفظ البيانات المحدثة
        with open(predictions_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=4, ensure_ascii=False)
            
        print("✅ تم تنظيف التنبؤات القديمة بنجاح")
        
    except Exception as e:
        print(f"❌ حدث خطأ أثناء تنظيف التنبؤات القديمة: {e}")

def update_predictions():
    """
    تحديث المباريات وعمل تنبؤات جديدة
    """
    try:
        # تحديث المباريات القادمة
        subprocess.run([
            "python",
            "scripts/update_matches.py",
            "--output_file",
            "data/next_matches.json"
        ], check=True)
        
        # عمل تنبؤات جديدة
        subprocess.run([
            "python",
            "scripts/make_predictions.py",
            "--input_leagues_models_dir",
            "models",
            "--input_data_predict_dir",
            "data/processed",
            "--final_predictions_out_file",
            "data/final_predictions.txt",
            "--next_matches",
            "data/next_matches.json"
        ], check=True)
        
        print("✅ تم تحديث التنبؤات بنجاح")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ حدث خطأ أثناء تحديث التنبؤات: {e}")

def main():
    predictions_file = "web/predictions.json"
    
    print("🔄 جاري تنظيف التنبؤات القديمة...")
    clean_old_predictions(predictions_file)
    
    print("\n🔄 جاري تحديث التنبؤات...")
    update_predictions()

if __name__ == "__main__":
    main()
