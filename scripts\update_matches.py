"""
Script to update upcoming matches from API-Football.

Usage:
------
python scripts/update_matches.py --output_file data/next_matches.json
"""

import os
import json
import time
import argparse
from datetime import datetime, timedelta
import requests

# API Football Configuration
# API key from data_acquisition.py
API_FOOTBALL_KEY = 'a76425798e481275c75572b8d37a40d1'
API_FOOTBALL_HOST = 'v3.football.api-sports.io'

# Timeout settings for API requests
REQUEST_TIMEOUT = 30  # seconds
MAX_RETRIES = 3

# League IDs mapping (same as in data_acquisition.py)
API_FOOTBALL_LEAGUES = {
    # الدوريات الإنجليزية
    'E0': 39,    # Premier League
    'E1': 40,    # Championship
    'E2': 41,    # League One
    'E3': 42,    # League Two
    'EC': 43,    # National League

    # الدوريات الاسكتلندية
    'SC0': 179,  # Premiership
    'SC1': 180,  # Championship
    'SC2': 181,  # League One
    'SC3': 182,  # League Two

    # الدوريات الأوروبية الكبرى
    'I1': 135,   # Serie A
    'I2': 136,   # Serie B
    'D1': 78,    # Bundesliga
    'D2': 79,    # 2. Bundesliga
    'SP1': 140,  # La Liga
    'SP2': 141,  # La Liga 2
    'F1': 61,    # Ligue 1
    'F2': 62,    # Ligue 2

    # دوريات أوروبية أخرى
    'N1': 88,    # Eredivisie
    'P1': 94,    # Primeira Liga
    'B1': 144,   # Jupiler Pro League
    'T1': 203,   # Super Lig
    'G1': 197,   # Super League

    # دوريات أمريكا الجنوبية
    'ARG': 128,  # Argentina Liga Profesional
    'BRA': 71,   # Brasileirão
    'URY': 268,  # Primera División Uruguay
    'CHL': 265,  # Primera División Chile
    'COL': 239,  # Primera A Colombia

    # دوريات أمريكا الشمالية والوسطى
    'MEX': 262,  # Liga MX
    'USA': 253,  # MLS
    'CAN': 254,  # Canadian Premier League

    # دوريات آسيوية
    'JPN': 98,   # J1 League
    'KOR': 292,  # K League 1
    'CHN': 169,  # Super League
    'SAU': 307,  # Saudi Pro League
    'QAT': 305,  # Qatar Stars League
    'UAE': 297,  # UAE Pro League

    # دوريات أفريقية
    'EGY': 233,  # Egyptian Premier League
    'MAR': 200,  # Botola Pro
    'TUN': 202,  # Ligue 1
    'RSA': 288,  # Premier Soccer League

    # دوريات أوقيانوسيا
    'AUS': 188,  # A-League
    'NZL': 205,  # New Zealand Football Championship
}

def get_upcoming_matches(league_id, days_ahead=2):
    """
    Get upcoming matches for a specific league from API-Football.
    
    Parameters:
    -----------
    league_id : int
        The league ID in API-Football
    days_ahead : int
        Number of days to look ahead for fixtures
    
    Returns:
    --------
    list
        List of upcoming matches
    """
    # Calculate date range
    from_date = datetime.now().strftime("%Y-%m-%d")
    to_date = (datetime.now() + timedelta(days=days_ahead)).strftime("%Y-%m-%d")
    
    # API endpoint
    url = f"https://v3.football.api-sports.io/fixtures"
    
    # Parameters for the API call
    params = {
        'league': league_id,
        'season': '2024',  # Current season
        'from': from_date,
        'to': to_date,
    }
    
    # Headers required by API-Football
    headers = {
        'x-rapidapi-host': API_FOOTBALL_HOST,
        'x-rapidapi-key': API_FOOTBALL_KEY
    }
    
    for attempt in range(MAX_RETRIES):
        try:
            response = requests.get(url, params=params, headers=headers, timeout=REQUEST_TIMEOUT)
            response.raise_for_status()
            
            data = response.json()
            
            if data['results'] == 0:
                print(f"No upcoming matches found for league {league_id}")
                return []
            
            matches = []
            for fixture in data['response']:
                match = {
                    'date': fixture['fixture']['date'],
                    'home_team': fixture['teams']['home']['name'],
                    'away_team': fixture['teams']['away']['name'],
                    'home_team_crest': fixture['teams']['home']['logo'],
                    'away_team_crest': fixture['teams']['away']['logo'],
                    'league': league_id
                }
                matches.append(match)
            
            print(f"Successfully fetched {len(matches)} matches for league {league_id}")
            return matches
        
        except requests.Timeout:
            if attempt < MAX_RETRIES - 1:
                print(f"Timeout error for league {league_id}, attempt {attempt + 1}/{MAX_RETRIES}. Retrying...")
                time.sleep(2 ** attempt)  # Exponential backoff
            else:
                print(f"Timeout error for league {league_id} after {MAX_RETRIES} attempts")
                return []
        
        except requests.RequestException as e:
            print(f"Error fetching matches for league {league_id}: {e}")
            return []
        
        except Exception as e:
            print(f"Unexpected error for league {league_id}: {e}")
            return []

def update_matches(output_file):
    """
    Update matches for all leagues and save to JSON file.
    
    Args:
        output_file (str): Path to output JSON file.
    """
    all_leagues = {}
    
    # Fetch matches for each league
    for league_code, league_id in API_FOOTBALL_LEAGUES.items():
        print(f"Fetching matches for {league_code}...")
        matches = get_upcoming_matches(league_id)
        
        if matches:  # Only add leagues with matches
            league_info = {
                'id': league_id,
                'name': league_code,
                'next_matches': matches
            }
            all_leagues[league_code] = league_info
    
    # Create output directory if it doesn't exist
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    
    # Save all matches to JSON file
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(all_leagues, f, indent=4, ensure_ascii=False)
    
    print(f"\nUpdated matches saved to {output_file}")

def parse_arguments():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(description="Update upcoming matches from API-Football.")
    parser.add_argument('--output_file', type=str, required=True,
                      help="Path to save the updated matches JSON file")
    return parser.parse_args()

if __name__ == "__main__":
    args = parse_arguments()
    update_matches(args.output_file)
