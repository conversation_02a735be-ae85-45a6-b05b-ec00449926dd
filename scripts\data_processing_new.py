"""
معالجة بيانات المباريات وإعداد الميزات للتنبؤ

Usage:
------
Run this script from the terminal as follows:

    python scripts/data_processing.py --input_dir data/raw --output_dir data/processed --num_features 20 --clustering_threshold 0.5

Parameters:
-----------
input_dir : str
    مجلد البيانات الخام
output_dir : str
    مجلد حفظ البيانات المعالجة
num_features : int
    عدد الميزات المهمة التي سيتم اختيارها (default: 20)
clustering_threshold : float
    حد التجميع للميزات المتشابهة (default: 0.5)
"""

import os
import argparse
import pandas as pd
import numpy as np
from datetime import datetime
import scipy.cluster.hierarchy as sch
from mrmr import mrmr_classif
from sklearn.preprocessing import StandardScaler

def process_basic_features(df):
    """
    المعالجة الأساسية للبيانات وإضافة ميزات جديدة
    """
    # تحويل التاريخ إلى صيغة موحدة
    df['Date'] = pd.to_datetime(df['Date'], format='%d/%m/%y')
    
    # إضافة ميزات جديدة
    df['TotalGoals'] = df['FTHG'] + df['FTAG']
    df['GoalDiff'] = df['FTHG'] - df['FTAG']
    df['IsOver2_5'] = (df['TotalGoals'] > 2.5).astype(int)
    
    # حساب معدلات الأهداف المتحركة
    df = df.sort_values('Date')
    for team in pd.concat([df['HomeTeam'], df['AwayTeam']]).unique():
        # معدل تسجيل الأهداف في آخر 5 مباريات
        mask_home = df['HomeTeam'] == team
        mask_away = df['AwayTeam'] == team
        
        df.loc[mask_home, 'Last5_HomeGoals'] = df.loc[mask_home, 'FTHG'].rolling(5, min_periods=1).mean()
        df.loc[mask_away, 'Last5_AwayGoals'] = df.loc[mask_away, 'FTAG'].rolling(5, min_periods=1).mean()
        
        # معدل استقبال الأهداف في آخر 5 مباريات
        df.loc[mask_home, 'Last5_HomeConceded'] = df.loc[mask_home, 'FTAG'].rolling(5, min_periods=1).mean()
        df.loc[mask_away, 'Last5_AwayConceded'] = df.loc[mask_away, 'FTHG'].rolling(5, min_periods=1).mean()
    
    return df

def select_features(df, num_features, clustering_threshold):
    """
    اختيار أفضل الميزات باستخدام mRMR وتجميع الميزات المتشابهة
    """
    # تحديد الأعمدة الرقمية
    numeric_columns = df.select_dtypes(include=[np.number]).columns
    feature_columns = numeric_columns.drop('IsOver2_5') if 'IsOver2_5' in numeric_columns else numeric_columns
    
    # حساب مصفوفة الارتباط
    correlation_matrix = df[feature_columns].corr().fillna(0)
    
    # تجميع الميزات المتشابهة
    dissimilarity = 1 - abs(correlation_matrix)
    hierarchy = sch.linkage(sch.distance.squareform(dissimilarity), method='average')
    clusters = sch.fcluster(hierarchy, clustering_threshold, criterion='distance')
    
    # اختيار ممثل واحد من كل مجموعة
    cluster_representatives = []
    for cluster_id in np.unique(clusters):
        cluster_features = feature_columns[clusters == cluster_id]
        if len(cluster_features) > 0:
            # اختيار الميزة الأكثر تباينًا في المجموعة
            variances = df[cluster_features].var()
            representative = variances.idxmax()
            cluster_representatives.append(representative)
    
    # اختيار أفضل الميزات باستخدام mRMR
    X = df[cluster_representatives]
    y = df['IsOver2_5']
    selected_features = mrmr_classif(X, y, K=min(num_features, len(cluster_representatives)))
    
    return selected_features

def process_league_data(input_file, output_dir, league_code, num_features=20, clustering_threshold=0.5):
    """
    معالجة بيانات دوري معين
    """
    try:
        print(f"\nمعالجة بيانات {league_code}...")
        
        # قراءة البيانات
        df = pd.read_csv(input_file)
        
        # المعالجة الأساسية وإضافة الميزات
        df = process_basic_features(df)
        
        # ملء القيم المفقودة
        numeric_columns = df.select_dtypes(include=[np.number]).columns
        df[numeric_columns] = df[numeric_columns].fillna(df[numeric_columns].mean())
        
        # اختيار أفضل الميزات
        selected_features = select_features(df, num_features, clustering_threshold)
        
        # إضافة عمود الهدف
        final_columns = list(selected_features) + ['IsOver2_5']
        
        # تطبيع البيانات
        scaler = StandardScaler()
        df[selected_features] = scaler.fit_transform(df[selected_features])
        
        # حفظ البيانات المعالجة
        output_file = os.path.join(output_dir, f"{league_code}_processed.csv")
        df[final_columns].to_csv(output_file, index=False)
        print(f"✅ تم حفظ البيانات المعالجة في {output_file}")
        
        # حفظ معلومات التطبيع
        scaler_info = {
            'mean': scaler.mean_.tolist(),
            'scale': scaler.scale_.tolist(),
            'features': list(selected_features)
        }
        scaler_file = os.path.join(output_dir, f"{league_code}_scaler.json")
        pd.DataFrame(scaler_info).to_json(scaler_file)
        print(f"✅ تم حفظ معلومات التطبيع في {scaler_file}")
        
    except Exception as e:
        print(f"❌ خطأ في معالجة {league_code}: {str(e)}")

def main():
    parser = argparse.ArgumentParser(description="معالجة بيانات المباريات وإعداد الميزات")
    parser.add_argument("--input_dir", required=True, help="مجلد البيانات الخام")
    parser.add_argument("--output_dir", required=True, help="مجلد حفظ البيانات المعالجة")
    parser.add_argument("--num_features", type=int, default=20, help="عدد الميزات المهمة التي سيتم اختيارها")
    parser.add_argument("--clustering_threshold", type=float, default=0.5, help="حد التجميع للميزات المتشابهة")
    args = parser.parse_args()
    
    # إنشاء مجلد المخرجات إذا لم يكن موجوداً
    os.makedirs(args.output_dir, exist_ok=True)
    
    # معالجة كل ملفات البيانات
    for filename in os.listdir(args.input_dir):
        if filename.endswith("_merged.csv"):
            league_code = filename.split("_")[0]
            input_file = os.path.join(args.input_dir, filename)
            process_league_data(
                input_file, 
                args.output_dir, 
                league_code,
                args.num_features,
                args.clustering_threshold
            )

if __name__ == "__main__":
    main()
