// Dashboard JavaScript for AI Football Predictions System
class FootballDashboard {
    constructor() {
        this.apiBaseUrl = '/api';
        this.currentProcess = null;
        this.logs = [];
        this.predictions = [];
        this.charts = {};
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadInitialData();
        this.setupCharts();
        this.startAutoRefresh();
    }

    setupEventListeners() {
        // Range sliders
        document.getElementById('modelAccuracy').addEventListener('input', (e) => {
            document.getElementById('accuracyValue').textContent = e.target.value + '%';
        });

        document.getElementById('confidenceThreshold').addEventListener('input', (e) => {
            document.getElementById('confidenceValue').textContent = e.target.value + '%';
        });

        // Auto-refresh checkbox
        document.getElementById('autoUpdate').addEventListener('change', (e) => {
            if (e.target.checked) {
                this.startAutoRefresh();
            } else {
                this.stopAutoRefresh();
            }
        });
    }

    async loadInitialData() {
        try {
            await this.loadPredictions();
            await this.loadStats();
            this.updateLastUpdateTime();
        } catch (error) {
            this.addLog('خطأ في تحميل البيانات الأولية: ' + error.message, 'error');
        }
    }

    async loadPredictions() {
        try {
            const response = await fetch('/api/predictions');
            if (response.ok) {
                const result = await response.json();
                if (result.success) {
                    this.predictions = result.data;
                    this.displayPredictions();
                    this.updateStats();
                } else {
                    throw new Error(result.error || 'فشل في تحميل التنبؤات');
                }
            } else {
                throw new Error('فشل في الاتصال بالخادم');
            }
        } catch (error) {
            this.addLog('خطأ في تحميل التنبؤات: ' + error.message, 'error');
            this.showNoPredictions();
        }
    }

    async loadStats() {
        try {
            const response = await fetch('/api/stats');
            if (response.ok) {
                const result = await response.json();
                if (result.success) {
                    const stats = result.data;
                    document.getElementById('totalMatches').textContent = stats.total_matches;
                    document.getElementById('totalLeagues').textContent = stats.total_leagues;

                    if (stats.last_update) {
                        const lastUpdate = new Date(stats.last_update).toLocaleString('ar-SA');
                        document.getElementById('lastUpdate').textContent = lastUpdate;
                    }
                }
            }
        } catch (error) {
            this.addLog('خطأ في تحميل الإحصائيات: ' + error.message, 'error');
        }
    }

    displayPredictions() {
        const container = document.getElementById('predictionsContainer');
        
        if (!this.predictions || Object.keys(this.predictions).length === 0) {
            this.showNoPredictions();
            return;
        }

        let html = '';
        const leagueGroups = this.groupLeagues();

        for (const [groupName, leagues] of Object.entries(leagueGroups)) {
            if (leagues.length > 0) {
                html += `
                    <div class="league-section">
                        <div class="league-title">
                            <i class="fas fa-trophy me-2"></i>
                            ${groupName}
                        </div>
                        <div class="row">
                `;

                leagues.forEach(league => {
                    if (this.predictions[league] && this.predictions[league].next_matches) {
                        this.predictions[league].next_matches.forEach(match => {
                            const prediction = match.prediction || 'غير متوفر';
                            const confidence = match.confidence ? Math.round(match.confidence * 100) : 0;
                            const predictionClass = prediction.includes('Over') ? 'over' : 'under';
                            const predictionText = prediction.includes('Over') ? 'أكثر من 2.5' : 'أقل من 2.5';
                            const predictionIcon = prediction.includes('Over') ? 'fa-arrow-up' : 'fa-arrow-down';

                            html += `
                                <div class="col-md-6 mb-3">
                                    <div class="match ${predictionClass}">
                                        <div class="match-time">
                                            <i class="fas fa-clock me-1"></i>
                                            ${this.formatDate(match.date)}
                                        </div>
                                        <div class="team-vs">
                                            ${match.home_team} vs ${match.away_team}
                                        </div>
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div class="prediction-${predictionClass}">
                                                <i class="fas ${predictionIcon} me-1"></i>
                                                ${predictionText}
                                            </div>
                                            <div class="confidence">
                                                <i class="fas fa-percentage me-1"></i>
                                                ${confidence}% ثقة
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            `;
                        });
                    }
                });

                html += `
                        </div>
                    </div>
                `;
            }
        }

        container.innerHTML = html;
    }

    groupLeagues() {
        const majorLeagues = ['E0', 'SP1', 'I1', 'D1', 'F1'];
        const arabLeagues = ['SAU', 'EGY', 'UAE', 'QAT', 'TUN', 'MAR'];
        
        const groups = {
            'الدوريات الأوروبية الكبرى': [],
            'الدوريات العربية': [],
            'دوريات أخرى': []
        };

        Object.keys(this.predictions).forEach(league => {
            if (majorLeagues.includes(league)) {
                groups['الدوريات الأوروبية الكبرى'].push(league);
            } else if (arabLeagues.includes(league)) {
                groups['الدوريات العربية'].push(league);
            } else {
                groups['دوريات أخرى'].push(league);
            }
        });

        return groups;
    }

    showNoPredictions() {
        const container = document.getElementById('predictionsContainer');
        container.innerHTML = `
            <div class="text-center py-5">
                <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                <h4>لا توجد تنبؤات متاحة</h4>
                <p class="text-muted">قم بتحديث المباريات وعمل التنبؤات أولاً</p>
                <button class="btn btn-primary-custom btn-custom" onclick="dashboard.updateMatches()">
                    <i class="fas fa-sync-alt me-2"></i>
                    تحديث المباريات
                </button>
            </div>
        `;
    }

    updateStats() {
        let totalMatches = 0;
        let overPredictions = 0;
        let underPredictions = 0;

        Object.values(this.predictions).forEach(league => {
            if (league.next_matches) {
                totalMatches += league.next_matches.length;
                league.next_matches.forEach(match => {
                    if (match.prediction && match.prediction.includes('Over')) {
                        overPredictions++;
                    } else {
                        underPredictions++;
                    }
                });
            }
        });

        document.getElementById('totalMatches').textContent = totalMatches;
        
        // Update charts if they exist
        if (this.charts.predictionChart) {
            this.updatePredictionChart(overPredictions, underPredictions);
        }
    }

    async updateMatches() {
        this.setProcessStatus('updateStatus', 'loading');
        this.showProgress('جاري تحديث المباريات...');
        this.addLog('بدء تحديث المباريات...', 'info');

        try {
            const response = await this.executeScript('update_matches');
            if (response.success) {
                this.addLog('تم تحديث المباريات بنجاح', 'success');
                this.setProcessStatus('updateStatus', 'running');
                await this.loadPredictions();
            } else {
                throw new Error(response.error || 'فشل في تحديث المباريات');
            }
        } catch (error) {
            this.addLog('خطأ في تحديث المباريات: ' + error.message, 'error');
            this.setProcessStatus('updateStatus', 'stopped');
        } finally {
            this.hideProgress();
        }
    }

    async makePredictions() {
        this.setProcessStatus('predictStatus', 'loading');
        this.showProgress('جاري عمل التنبؤات...');
        this.addLog('بدء عمل التنبؤات...', 'info');

        try {
            const response = await this.executeScript('make_predictions');
            if (response.success) {
                this.addLog('تم عمل التنبؤات بنجاح', 'success');
                this.setProcessStatus('predictStatus', 'running');
                await this.loadPredictions();
            } else {
                throw new Error(response.error || 'فشل في عمل التنبؤات');
            }
        } catch (error) {
            this.addLog('خطأ في عمل التنبؤات: ' + error.message, 'error');
            this.setProcessStatus('predictStatus', 'stopped');
        } finally {
            this.hideProgress();
        }
    }

    async retrainModels() {
        this.setProcessStatus('trainStatus', 'loading');
        this.showProgress('جاري إعادة تدريب النماذج...');
        this.addLog('بدء إعادة تدريب النماذج...', 'info');

        try {
            const response = await this.executeScript('retrain_models');
            if (response.success) {
                this.addLog('تم إعادة تدريب النماذج بنجاح', 'success');
                this.setProcessStatus('trainStatus', 'running');
            } else {
                throw new Error(response.error || 'فشل في إعادة تدريب النماذج');
            }
        } catch (error) {
            this.addLog('خطأ في إعادة تدريب النماذج: ' + error.message, 'error');
            this.setProcessStatus('trainStatus', 'stopped');
        } finally {
            this.hideProgress();
        }
    }

    async downloadData() {
        this.setProcessStatus('downloadStatus', 'loading');
        this.showProgress('جاري تحميل البيانات...');
        this.addLog('بدء تحميل البيانات...', 'info');

        try {
            const response = await this.executeScript('download_data');
            if (response.success) {
                this.addLog('تم تحميل البيانات بنجاح', 'success');
                this.setProcessStatus('downloadStatus', 'running');
            } else {
                throw new Error(response.error || 'فشل في تحميل البيانات');
            }
        } catch (error) {
            this.addLog('خطأ في تحميل البيانات: ' + error.message, 'error');
            this.setProcessStatus('downloadStatus', 'stopped');
        } finally {
            this.hideProgress();
        }
    }

    async executeScript(scriptName) {
        try {
            const response = await fetch(`/api/${scriptName.replace('_', '-')}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();

            // If we got a process ID, monitor the process
            if (result.success && result.process_id) {
                this.monitorProcess(result.process_id);
            }

            return result;
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    async monitorProcess(processId) {
        const checkStatus = async () => {
            try {
                const response = await fetch(`/api/process-status/${processId}`);
                const result = await response.json();

                if (result.success && result.data.logs) {
                    // Add new logs
                    result.data.logs.forEach(log => {
                        this.addLog(log.message, log.type);
                    });

                    // If process is still running, check again
                    if (result.data.running) {
                        setTimeout(checkStatus, 2000);
                    }
                }
            } catch (error) {
                console.error('Error monitoring process:', error);
            }
        };

        checkStatus();
    }

    setProcessStatus(elementId, status) {
        const element = document.getElementById(elementId);
        element.className = `status-indicator status-${status}`;
    }

    showProgress(text) {
        const container = document.getElementById('progressContainer');
        const progressText = document.getElementById('progressText');
        const progressBar = document.getElementById('progressBar');
        
        container.style.display = 'block';
        progressText.textContent = text;
        
        // Simulate progress
        let progress = 0;
        const interval = setInterval(() => {
            progress += Math.random() * 10;
            if (progress > 90) progress = 90;
            progressBar.style.width = progress + '%';
        }, 200);
        
        this.currentProgressInterval = interval;
    }

    hideProgress() {
        const container = document.getElementById('progressContainer');
        const progressBar = document.getElementById('progressBar');
        
        if (this.currentProgressInterval) {
            clearInterval(this.currentProgressInterval);
        }
        
        progressBar.style.width = '100%';
        setTimeout(() => {
            container.style.display = 'none';
            progressBar.style.width = '0%';
        }, 500);
    }

    addLog(message, type = 'info') {
        const timestamp = new Date().toLocaleString('ar-SA');
        const log = {
            timestamp,
            message,
            type
        };
        
        this.logs.unshift(log);
        if (this.logs.length > 100) {
            this.logs = this.logs.slice(0, 100);
        }
        
        this.updateLogDisplay();
    }

    updateLogDisplay() {
        const container = document.getElementById('logContainer');
        
        if (this.logs.length === 0) {
            container.innerHTML = '<div class="text-center text-muted py-3">لا توجد عمليات مسجلة حتى الآن...</div>';
            return;
        }

        let html = '';
        this.logs.forEach(log => {
            const icon = this.getLogIcon(log.type);
            const color = this.getLogColor(log.type);
            html += `
                <div style="color: ${color}; margin-bottom: 5px;">
                    <span style="color: #888;">[${log.timestamp}]</span>
                    <i class="${icon}" style="margin: 0 5px;"></i>
                    ${log.message}
                </div>
            `;
        });
        
        container.innerHTML = html;
        container.scrollTop = 0;
    }

    getLogIcon(type) {
        const icons = {
            'info': 'fas fa-info-circle',
            'success': 'fas fa-check-circle',
            'error': 'fas fa-exclamation-circle',
            'warning': 'fas fa-exclamation-triangle'
        };
        return icons[type] || icons.info;
    }

    getLogColor(type) {
        const colors = {
            'info': '#00ff00',
            'success': '#00ff00',
            'error': '#ff0000',
            'warning': '#ffff00'
        };
        return colors[type] || colors.info;
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleString('ar-SA', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    updateLastUpdateTime() {
        const now = new Date();
        const timeString = now.toLocaleString('ar-SA');
        document.getElementById('lastUpdateTime').textContent = timeString;
    }

    startAutoRefresh() {
        this.autoRefreshInterval = setInterval(() => {
            this.loadPredictions();
            this.updateLastUpdateTime();
        }, 60000); // Refresh every minute
    }

    stopAutoRefresh() {
        if (this.autoRefreshInterval) {
            clearInterval(this.autoRefreshInterval);
        }
    }

    setupCharts() {
        // Setup prediction distribution chart
        const ctx1 = document.getElementById('predictionChart');
        if (ctx1) {
            this.charts.predictionChart = new Chart(ctx1, {
                type: 'doughnut',
                data: {
                    labels: ['أكثر من 2.5', 'أقل من 2.5'],
                    datasets: [{
                        data: [0, 0],
                        backgroundColor: ['#27ae60', '#e74c3c']
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }
    }

    updatePredictionChart(over, under) {
        if (this.charts.predictionChart) {
            this.charts.predictionChart.data.datasets[0].data = [over, under];
            this.charts.predictionChart.update();
        }
    }
}

// Global functions for HTML onclick events
function updateMatches() {
    dashboard.updateMatches();
}

function makePredictions() {
    dashboard.makePredictions();
}

function retrainModels() {
    dashboard.retrainModels();
}

function downloadData() {
    dashboard.downloadData();
}

function refreshPredictions() {
    dashboard.loadPredictions();
}

function exportPredictions() {
    // Implementation for exporting predictions
    Swal.fire({
        title: 'تصدير التنبؤات',
        text: 'سيتم تصدير التنبؤات قريباً',
        icon: 'info'
    });
}

function filterPredictions() {
    // Implementation for filtering predictions
    dashboard.displayPredictions();
}

function clearLogs() {
    dashboard.logs = [];
    dashboard.updateLogDisplay();
}

function downloadLogs() {
    // Implementation for downloading logs
    const logs = dashboard.logs.map(log => `[${log.timestamp}] ${log.message}`).join('\n');
    const blob = new Blob([logs], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'football_predictions_logs.txt';
    a.click();
}

function saveSettings() {
    Swal.fire({
        title: 'تم حفظ الإعدادات',
        text: 'تم حفظ جميع الإعدادات بنجاح',
        icon: 'success'
    });
}

// Initialize dashboard when page loads
let dashboard;
document.addEventListener('DOMContentLoaded', function() {
    dashboard = new FootballDashboard();
});
