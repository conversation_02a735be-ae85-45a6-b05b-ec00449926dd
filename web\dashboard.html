<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم نظام توقعات كرة القدم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --danger-color: #e74c3c;
            --warning-color: #f39c12;
            --info-color: #17a2b8;
            --light-color: #ecf0f1;
            --dark-color: #2c3e50;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }

        .navbar {
            background: rgba(44, 62, 80, 0.95) !important;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
        }

        .main-container {
            margin-top: 20px;
            padding: 0 15px;
        }

        .control-panel {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .stats-card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .btn-custom {
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
            margin: 5px;
            position: relative;
            overflow: hidden;
        }

        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn-primary-custom {
            background: linear-gradient(135deg, var(--secondary-color), #5dade2);
            color: white;
        }

        .btn-success-custom {
            background: linear-gradient(135deg, var(--success-color), #58d68d);
            color: white;
        }

        .btn-warning-custom {
            background: linear-gradient(135deg, var(--warning-color), #f7dc6f);
            color: white;
        }

        .btn-danger-custom {
            background: linear-gradient(135deg, var(--danger-color), #ec7063);
            color: white;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }

        .status-running {
            background-color: var(--success-color);
            animation: pulse 2s infinite;
        }

        .status-stopped {
            background-color: var(--danger-color);
        }

        .status-loading {
            background-color: var(--warning-color);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .progress-custom {
            height: 8px;
            border-radius: 10px;
            background-color: rgba(255,255,255,0.3);
        }

        .progress-bar-custom {
            border-radius: 10px;
            transition: width 0.3s ease;
        }

        .log-container {
            background: rgba(0, 0, 0, 0.8);
            color: #00ff00;
            border-radius: 10px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 15px;
        }

        .modal-custom .modal-content {
            border-radius: 15px;
            border: none;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .modal-custom .modal-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-radius: 15px 15px 0 0;
            border-bottom: none;
        }

        .form-control-custom {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }

        .form-control-custom:focus {
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }

        .league-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .match {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 12px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
        }

        .match:hover {
            transform: translateX(-5px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
        }

        .match.over {
            border-left-color: var(--success-color);
        }

        .match.under {
            border-left-color: var(--danger-color);
        }

        .prediction-over {
            color: var(--success-color);
            font-weight: bold;
        }

        .prediction-under {
            color: var(--danger-color);
            font-weight: bold;
        }

        .confidence {
            font-size: 0.9em;
            color: #6c757d;
            font-weight: 500;
        }

        .team-vs {
            font-size: 1.1em;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .match-time {
            font-size: 0.85em;
            color: #6c757d;
            margin-bottom: 5px;
        }

        .spinner-custom {
            width: 2rem;
            height: 2rem;
            border: 3px solid rgba(255,255,255,0.3);
            border-top: 3px solid var(--secondary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .tab-content {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 0 15px 15px 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }

        .nav-tabs .nav-link {
            border-radius: 15px 15px 0 0;
            border: none;
            background: rgba(255, 255, 255, 0.7);
            color: var(--dark-color);
            font-weight: 500;
            margin-left: 5px;
        }

        .nav-tabs .nav-link.active {
            background: rgba(255, 255, 255, 0.95);
            color: var(--primary-color);
            font-weight: bold;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-futbol me-2"></i>
                نظام توقعات كرة القدم AI
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="#dashboard">
                            <i class="fas fa-tachometer-alt me-1"></i>
                            لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#predictions">
                            <i class="fas fa-chart-line me-1"></i>
                            التنبؤات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#settings">
                            <i class="fas fa-cog me-1"></i>
                            الإعدادات
                        </a>
                    </li>
                </ul>
                <span class="navbar-text">
                    <i class="fas fa-clock me-1"></i>
                    آخر تحديث: <span id="lastUpdateTime">--</span>
                </span>
            </div>
        </div>
    </nav>

    <!-- Main Container -->
    <div class="container-fluid main-container">
        <!-- Control Panel -->
        <div class="control-panel">
            <div class="row">
                <div class="col-md-12">
                    <h2 class="mb-4">
                        <i class="fas fa-control me-2"></i>
                        لوحة التحكم الرئيسية
                    </h2>
                </div>
            </div>
            
            <!-- Action Buttons -->
            <div class="row mb-4">
                <div class="col-md-3 col-sm-6 mb-3">
                    <button class="btn btn-primary-custom btn-custom w-100" onclick="updateMatches()">
                        <i class="fas fa-sync-alt me-2"></i>
                        تحديث المباريات
                        <span class="status-indicator" id="updateStatus"></span>
                    </button>
                </div>
                <div class="col-md-3 col-sm-6 mb-3">
                    <button class="btn btn-success-custom btn-custom w-100" onclick="makePredictions()">
                        <i class="fas fa-brain me-2"></i>
                        عمل التنبؤات
                        <span class="status-indicator" id="predictStatus"></span>
                    </button>
                </div>
                <div class="col-md-3 col-sm-6 mb-3">
                    <button class="btn btn-warning-custom btn-custom w-100" onclick="retrainModels()">
                        <i class="fas fa-robot me-2"></i>
                        إعادة تدريب النماذج
                        <span class="status-indicator" id="trainStatus"></span>
                    </button>
                </div>
                <div class="col-md-3 col-sm-6 mb-3">
                    <button class="btn btn-danger-custom btn-custom w-100" onclick="downloadData()">
                        <i class="fas fa-download me-2"></i>
                        تحميل البيانات
                        <span class="status-indicator" id="downloadStatus"></span>
                    </button>
                </div>
            </div>

            <!-- Progress Bar -->
            <div class="row mb-3" id="progressContainer" style="display: none;">
                <div class="col-12">
                    <div class="progress progress-custom">
                        <div class="progress-bar progress-bar-custom bg-success" id="progressBar" role="progressbar" style="width: 0%"></div>
                    </div>
                    <small class="text-muted mt-1 d-block" id="progressText">جاري المعالجة...</small>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="stats-card text-center">
                    <i class="fas fa-futbol fa-2x text-primary mb-2"></i>
                    <h4 id="totalMatches">0</h4>
                    <p class="text-muted mb-0">مباريات اليوم</p>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="stats-card text-center">
                    <i class="fas fa-trophy fa-2x text-success mb-2"></i>
                    <h4 id="totalLeagues">39</h4>
                    <p class="text-muted mb-0">دوري مدعوم</p>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="stats-card text-center">
                    <i class="fas fa-chart-line fa-2x text-warning mb-2"></i>
                    <h4 id="accuracyRate">--</h4>
                    <p class="text-muted mb-0">معدل الدقة</p>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="stats-card text-center">
                    <i class="fas fa-clock fa-2x text-info mb-2"></i>
                    <h4 id="lastUpdate">--</h4>
                    <p class="text-muted mb-0">آخر تحديث</p>
                </div>
            </div>
        </div>

        <!-- Tabs -->
        <ul class="nav nav-tabs" id="mainTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="predictions-tab" data-bs-toggle="tab" data-bs-target="#predictions" type="button" role="tab">
                    <i class="fas fa-chart-line me-1"></i>
                    التنبؤات
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="logs-tab" data-bs-toggle="tab" data-bs-target="#logs" type="button" role="tab">
                    <i class="fas fa-terminal me-1"></i>
                    سجل العمليات
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="settings-tab" data-bs-toggle="tab" data-bs-target="#settings" type="button" role="tab">
                    <i class="fas fa-cog me-1"></i>
                    الإعدادات
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="analysis-tab" data-bs-toggle="tab" data-bs-target="#analysis" type="button" role="tab">
                    <i class="fas fa-analytics me-1"></i>
                    التحليل
                </button>
            </li>
        </ul>

        <!-- Tab Content -->
        <div class="tab-content" id="mainTabContent">
            <!-- Predictions Tab -->
            <div class="tab-pane fade show active" id="predictions" role="tabpanel">
                <div class="row">
                    <div class="col-md-12 mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <h3>
                                <i class="fas fa-chart-line me-2"></i>
                                التنبؤات الحالية
                            </h3>
                            <div>
                                <button class="btn btn-outline-primary btn-sm" onclick="refreshPredictions()">
                                    <i class="fas fa-refresh me-1"></i>
                                    تحديث
                                </button>
                                <button class="btn btn-outline-success btn-sm" onclick="exportPredictions()">
                                    <i class="fas fa-download me-1"></i>
                                    تصدير
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filter Options -->
                <div class="row mb-3">
                    <div class="col-md-4">
                        <select class="form-select form-control-custom" id="leagueFilter" onchange="filterPredictions()">
                            <option value="">جميع الدوريات</option>
                            <option value="major">الدوريات الأوروبية الكبرى</option>
                            <option value="arab">الدوريات العربية</option>
                            <option value="other">دوريات أخرى</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <select class="form-select form-control-custom" id="confidenceFilter" onchange="filterPredictions()">
                            <option value="">جميع مستويات الثقة</option>
                            <option value="high">عالية (>80%)</option>
                            <option value="medium">متوسطة (60-80%)</option>
                            <option value="low">منخفضة (<60%)</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <select class="form-select form-control-custom" id="predictionFilter" onchange="filterPredictions()">
                            <option value="">جميع التنبؤات</option>
                            <option value="over">أكثر من 2.5</option>
                            <option value="under">أقل من 2.5</option>
                        </select>
                    </div>
                </div>

                <!-- Predictions Container -->
                <div id="predictionsContainer">
                    <div class="text-center py-5">
                        <div class="spinner-custom mx-auto mb-3"></div>
                        <p class="text-muted">جاري تحميل التنبؤات...</p>
                    </div>
                </div>
            </div>

            <!-- Logs Tab -->
            <div class="tab-pane fade" id="logs" role="tabpanel">
                <div class="row">
                    <div class="col-md-12 mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <h3>
                                <i class="fas fa-terminal me-2"></i>
                                سجل العمليات
                            </h3>
                            <div>
                                <button class="btn btn-outline-warning btn-sm" onclick="clearLogs()">
                                    <i class="fas fa-trash me-1"></i>
                                    مسح السجل
                                </button>
                                <button class="btn btn-outline-info btn-sm" onclick="downloadLogs()">
                                    <i class="fas fa-download me-1"></i>
                                    تحميل السجل
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="log-container" id="logContainer">
                    <div class="text-center text-muted py-3">
                        لا توجد عمليات مسجلة حتى الآن...
                    </div>
                </div>
            </div>

            <!-- Settings Tab -->
            <div class="tab-pane fade" id="settings" role="tabpanel">
                <div class="row">
                    <div class="col-md-6">
                        <h4>
                            <i class="fas fa-cog me-2"></i>
                            إعدادات النظام
                        </h4>

                        <div class="mb-3">
                            <label for="apiKey" class="form-label">مفتاح API Football</label>
                            <input type="password" class="form-control form-control-custom" id="apiKey" placeholder="أدخل مفتاح API">
                        </div>

                        <div class="mb-3">
                            <label for="updateInterval" class="form-label">فترة التحديث التلقائي (بالدقائق)</label>
                            <input type="number" class="form-control form-control-custom" id="updateInterval" value="60" min="5" max="1440">
                        </div>

                        <div class="mb-3">
                            <label for="selectedLeagues" class="form-label">الدوريات المختارة</label>
                            <select multiple class="form-select form-control-custom" id="selectedLeagues" size="8">
                                <option value="E0" selected>الدوري الإنجليزي الممتاز</option>
                                <option value="SP1" selected>الدوري الإسباني</option>
                                <option value="I1" selected>الدوري الإيطالي</option>
                                <option value="D1" selected>الدوري الألماني</option>
                                <option value="F1" selected>الدوري الفرنسي</option>
                                <option value="SAU">الدوري السعودي</option>
                                <option value="EGY">الدوري المصري</option>
                                <option value="UAE">الدوري الإماراتي</option>
                            </select>
                        </div>

                        <button class="btn btn-success-custom btn-custom" onclick="saveSettings()">
                            <i class="fas fa-save me-2"></i>
                            حفظ الإعدادات
                        </button>
                    </div>

                    <div class="col-md-6">
                        <h4>
                            <i class="fas fa-chart-bar me-2"></i>
                            إعدادات النماذج
                        </h4>

                        <div class="mb-3">
                            <label for="modelAccuracy" class="form-label">الحد الأدنى لدقة النموذج (%)</label>
                            <input type="range" class="form-range" id="modelAccuracy" min="50" max="95" value="75">
                            <div class="d-flex justify-content-between">
                                <small>50%</small>
                                <small id="accuracyValue">75%</small>
                                <small>95%</small>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="confidenceThreshold" class="form-label">حد الثقة للتنبؤات (%)</label>
                            <input type="range" class="form-range" id="confidenceThreshold" min="50" max="90" value="60">
                            <div class="d-flex justify-content-between">
                                <small>50%</small>
                                <small id="confidenceValue">60%</small>
                                <small>90%</small>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="autoRetrain" checked>
                                <label class="form-check-label" for="autoRetrain">
                                    إعادة التدريب التلقائي أسبوعياً
                                </label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="autoUpdate" checked>
                                <label class="form-check-label" for="autoUpdate">
                                    التحديث التلقائي للمباريات
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Analysis Tab -->
            <div class="tab-pane fade" id="analysis" role="tabpanel">
                <div class="row">
                    <div class="col-md-12 mb-3">
                        <h3>
                            <i class="fas fa-analytics me-2"></i>
                            تحليل الأداء
                        </h3>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="stats-card">
                            <h5>إحصائيات التنبؤات</h5>
                            <canvas id="predictionChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="stats-card">
                            <h5>دقة النماذج حسب الدوري</h5>
                            <canvas id="accuracyChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-md-12">
                        <div class="stats-card">
                            <h5>تطور الأداء عبر الوقت</h5>
                            <canvas id="performanceChart" width="800" height="300"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modals -->
    <!-- League Selection Modal -->
    <div class="modal fade modal-custom" id="leagueModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-list me-2"></i>
                        اختيار الدوريات
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row" id="leagueSelection">
                        <!-- League checkboxes will be populated here -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary-custom" onclick="applyLeagueSelection()">تطبيق</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="dashboard.js"></script>
</body>
</html>
