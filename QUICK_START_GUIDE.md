# 🚀 دليل البدء السريع - نظام توقعات كرة القدم

## 📋 البدء السريع (5 دقائق)

### 1. تشغيل النظام
```bash
# انقر مرتين على الملف
start_web_dashboard.bat
```

### 2. فتح لوحة التحكم
- افتح المتصفح
- اذهب إلى: http://localhost:5000/dashboard.html

### 3. تحديث المباريات
- اضغط على زر "تحديث المباريات" 🔄
- انتظر حتى تكتمل العملية

### 4. عمل التنبؤات
- اضغط على زر "عمل التنبؤات" 🧠
- شاهد النتائج في تبويب "التنبؤات"

## 🎯 الاستخدام اليومي

### كل صباح:
1. شغل `daily_web_update.bat`
2. أو اضغط "تحديث المباريات" في الواجهة
3. راجع التنبؤات الجديدة

### أسبوعياً:
1. اضغط "إعادة تدريب النماذج" 🤖
2. انتظر حتى تكتمل العملية (قد تستغرق وقتاً)

## 🔧 حل المشاكل السريع

### المشكلة: الخادم لا يبدأ
**الحل:**
```bash
pip install flask flask-cors
python scripts/web_server.py
```

### المشكلة: لا توجد تنبؤات
**الحل:**
1. اضغط "تحديث المباريات" أولاً
2. ثم اضغط "عمل التنبؤات"

### المشكلة: خطأ في API
**الحل:**
1. تحقق من اتصال الإنترنت
2. راجع مفتاح API في الإعدادات

## 📊 فهم النتائج

### نسبة الثقة:
- **🟢 عالية (80%+)**: توقع موثوق جداً
- **🟡 متوسطة (60-80%)**: توقع جيد
- **🔴 منخفضة (<60%)**: توقع ضعيف

### أنواع التنبؤات:
- **Over 2.5**: أكثر من 2.5 أهداف في المباراة
- **Under 2.5**: أقل من 2.5 أهداف في المباراة

## 🌐 الروابط المهمة

- **لوحة التحكم**: http://localhost:5000/dashboard.html
- **الواجهة الأصلية**: http://localhost:5000/index.html
- **API**: http://localhost:5000/api/

## 📱 نصائح سريعة

1. **استخدم الفلاتر** لتصفية التنبؤات حسب الدوري أو الثقة
2. **راقب السجل** لمتابعة العمليات الجارية
3. **حدث يومياً** للحصول على أفضل النتائج
4. **احفظ النتائج** قبل إغلاق المتصفح

## 🆘 الدعم السريع

إذا واجهت مشكلة:
1. تحقق من ملف `logs/daily_update.log`
2. أعد تشغيل النظام
3. تأكد من اتصال الإنترنت

---

**🎉 استمتع بالتوقعات الدقيقة!**
