import os
import argparse
import pandas as pd
import numpy as np
from datetime import datetime

def process_match_data(df):
    """
    معالجة بيانات المباريات وإعداد الميزات
    """
    # تحويل التاريخ إلى صيغة موحدة
    df['Date'] = pd.to_datetime(df['Date'], format='%d/%m/%y')
    
    # إضافة ميزات جديدة
    df['TotalGoals'] = df['FTHG'] + df['FTAG']
    df['GoalDiff'] = df['FTHG'] - df['FTAG']
    df['IsOver2_5'] = (df['TotalGoals'] > 2.5).astype(int)
    
    # حساب معدلات الأهداف المتحركة
    df = df.sort_values('Date')
    for team in pd.concat([df['HomeTeam'], df['AwayTeam']]).unique():
        # معدل تسجيل الأهداف في آخر 5 مباريات
        mask_home = df['HomeTeam'] == team
        mask_away = df['AwayTeam'] == team
        
        df.loc[mask_home, 'Last5_HomeGoals'] = df.loc[mask_home, 'FTHG'].rolling(5, min_periods=1).mean()
        df.loc[mask_away, 'Last5_AwayGoals'] = df.loc[mask_away, 'FTAG'].rolling(5, min_periods=1).mean()
        
        # معدل استقبال الأهداف في آخر 5 مباريات
        df.loc[mask_home, 'Last5_HomeConceded'] = df.loc[mask_home, 'FTAG'].rolling(5, min_periods=1).mean()
        df.loc[mask_away, 'Last5_AwayConceded'] = df.loc[mask_away, 'FTHG'].rolling(5, min_periods=1).mean()
    
    # ملء القيم المفقودة
    numeric_columns = df.select_dtypes(include=[np.number]).columns
    df[numeric_columns] = df[numeric_columns].fillna(df[numeric_columns].mean())
    
    return df

def process_league_data(input_file, output_dir, league_code):
    """
    معالجة بيانات دوري معين
    """
    try:
        # قراءة ملف البيانات
        df = pd.read_csv(input_file)
        
        # معالجة البيانات
        processed_df = process_match_data(df)
        
        # حفظ البيانات المعالجة
        output_file = os.path.join(output_dir, f"{league_code}_processed.csv")
        processed_df.to_csv(output_file, index=False)
        print(f"تم معالجة وحفظ بيانات {league_code} في {output_file}")
        
    except Exception as e:
        print(f"خطأ في معالجة {league_code}: {str(e)}")

def main():
    parser = argparse.ArgumentParser(description="معالجة بيانات المباريات وإعداد الميزات")
    parser.add_argument("--input_dir", required=True, help="مجلد البيانات الخام")
    parser.add_argument("--output_dir", required=True, help="مجلد حفظ البيانات المعالجة")
    args = parser.parse_args()
    
    # إنشاء مجلد المخرجات إذا لم يكن موجوداً
    os.makedirs(args.output_dir, exist_ok=True)
    
    # معالجة كل ملفات البيانات
    for filename in os.listdir(args.input_dir):
        if filename.endswith("_merged.csv"):
            league_code = filename.split("_")[0]
            input_file = os.path.join(args.input_dir, filename)
            process_league_data(input_file, args.output_dir, league_code)

if __name__ == "__main__":
    main()
