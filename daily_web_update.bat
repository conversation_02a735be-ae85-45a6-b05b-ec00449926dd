@echo off
chcp 65001 > nul
title التحديث اليومي لنظام توقعات كرة القدم

echo.
echo ========================================
echo    🔄 التحديث اليومي لنظام توقعات كرة القدم
echo    📅 %date% - %time%
echo ========================================
echo.

REM Create logs directory if it doesn't exist
if not exist "logs" (
    mkdir logs
)

echo 📋 بدء عملية التحديث اليومي...
echo.

REM Run the daily update script
python scripts\daily_web_update.py

if errorlevel 1 (
    echo.
    echo ❌ فشل في التحديث اليومي
    echo 📝 تحقق من ملف السجل: logs\daily_update.log
    echo.
    pause
    exit /b 1
) else (
    echo.
    echo ✅ تم التحديث اليومي بنجاح
    echo 🌐 يمكنك الآن زيارة لوحة التحكم: http://localhost:5000
    echo.
)

echo 📊 عرض ملخص التحديث...
echo.

REM Show update status if available
if exist "web\update_status.json" (
    echo 📈 حالة آخر تحديث:
    type web\update_status.json
    echo.
)

echo ✅ انتهى التحديث اليومي
pause
